const JobCard = ({ job, onEdit, onDelete }) => {
  const getStatusVariant = (status) => {
    const variants = {
      'Applied': 'applied',
      'Interviewing': 'interviewing',
      'Offer': 'offer',
      'Rejected': 'rejected',
      'Withdrawn': 'withdrawn'
    };
    return variants[status] || 'secondary';
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  return React.createElement(Card, { className: 'hover:shadow-md transition-shadow duration-200' },
    React.createElement(CardHeader, { className: 'pb-3' },
      React.createElement('div', { className: 'flex justify-between items-start gap-4' },
        React.createElement('div', { className: 'flex-1 min-w-0' },
          React.createElement(CardTitle, { className: 'text-lg mb-1 truncate' },
            job.position
          ),
          React.createElement('p', { className: 'text-muted-foreground font-medium mb-1' }, job.company),
          job.location && React.createElement('p', { className: 'text-sm text-muted-foreground truncate' },
            job.location
          )
        ),
        React.createElement(Badge, {
          variant: getStatusVariant(job.status),
          className: 'shrink-0'
        }, job.status)
      )
    ),

    React.createElement(CardContent, { className: 'space-y-4' },
      // Tags
      job.tags && job.tags.length > 0 && React.createElement('div', { className: 'flex flex-wrap gap-2' },
        job.tags.map(tag =>
          React.createElement(Badge, {
            key: tag.id,
            variant: 'outline',
            className: 'text-xs',
            style: {
              backgroundColor: `${tag.color}10`,
              color: tag.color,
              borderColor: `${tag.color}40`
            }
          }, tag.name)
        )
      ),

      // Additional info
      React.createElement('div', { className: 'space-y-2 text-sm' },
        job.salary_range && React.createElement('div', { className: 'flex items-center gap-2' },
          React.createElement('span', { className: 'font-medium text-muted-foreground min-w-0' }, 'Salary:'),
          React.createElement('span', { className: 'text-foreground' }, job.salary_range)
        ),
        job.application_date && React.createElement('div', { className: 'flex items-center gap-2' },
          React.createElement('span', { className: 'font-medium text-muted-foreground min-w-0' }, 'Applied:'),
          React.createElement('span', { className: 'text-foreground' }, formatDate(job.application_date))
        ),
        job.contact_person && React.createElement('div', { className: 'flex items-center gap-2' },
          React.createElement('span', { className: 'font-medium text-muted-foreground min-w-0' }, 'Contact:'),
          React.createElement('span', { className: 'text-foreground truncate' }, job.contact_person)
        )
      ),

      // Notes preview
      job.notes && React.createElement('div', { className: 'p-3 bg-muted/50 rounded-md' },
        React.createElement('p', { className: 'text-sm text-muted-foreground line-clamp-3' },
          job.notes.length > 120 ? `${job.notes.substring(0, 120)}...` : job.notes
        )
      )
    ),

    React.createElement(CardFooter, { className: 'flex flex-col gap-3 pt-4' },
      // External links
      (job.job_url || job.contact_email) && React.createElement('div', { className: 'flex flex-wrap gap-2 w-full' },
        job.job_url && React.createElement(Button, {
          variant: 'outline',
          size: 'sm',
          asChild: true,
          className: 'flex-1 min-w-0'
        },
          React.createElement('a', {
            href: job.job_url,
            target: '_blank',
            rel: 'noopener noreferrer'
          }, 'View Job')
        ),
        job.contact_email && React.createElement(Button, {
          variant: 'outline',
          size: 'sm',
          asChild: true,
          className: 'flex-1 min-w-0'
        },
          React.createElement('a', {
            href: `mailto:${job.contact_email}`
          }, 'Email Contact')
        )
      ),

      // Action buttons and last updated
      React.createElement('div', { className: 'flex justify-between items-center w-full' },
        React.createElement('div', { className: 'flex gap-2' },
          React.createElement(Button, {
            variant: 'ghost',
            size: 'sm',
            onClick: () => onEdit(job)
          }, 'Edit'),
          React.createElement(Button, {
            variant: 'ghost',
            size: 'sm',
            onClick: () => onDelete(job.id),
            className: 'text-destructive hover:text-destructive'
          }, 'Delete')
        ),
        React.createElement('span', { className: 'text-xs text-muted-foreground' },
          `Updated ${formatDate(job.last_updated)}`
        )
      )
    )
  );
};
