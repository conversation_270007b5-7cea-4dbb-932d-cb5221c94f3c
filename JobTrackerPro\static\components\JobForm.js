const JobForm = ({ job, tags, onSubmit, onClose, loading }) => {
  const [formData, setFormData] = useState({
    company: '',
    position: '',
    location: '',
    job_url: '',
    description: '',
    status: 'Applied',
    salary_range: '',
    application_date: new Date().toISOString().split('T')[0],
    notes: '',
    contact_person: '',
    contact_email: '',
    tag_ids: []
  });

  useEffect(() => {
    if (job) {
      setFormData({
        company: job.company || '',
        position: job.position || '',
        location: job.location || '',
        job_url: job.job_url || '',
        description: job.description || '',
        status: job.status || 'Applied',
        salary_range: job.salary_range || '',
        application_date: job.application_date ? job.application_date.split('T')[0] : new Date().toISOString().split('T')[0],
        notes: job.notes || '',
        contact_person: job.contact_person || '',
        contact_email: job.contact_email || '',
        tag_ids: job.tags ? job.tags.map(tag => tag.id) : []
      });
    }
  }, [job]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTagToggle = (tagId) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...prev.tag_ids, tagId]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.company || !formData.position) {
      alert('Company and position are required');
      return;
    }

    console.log('Submitting form data:', formData);
    await onSubmit(formData);
  };

  const statusOptions = [
    { value: 'Applied', label: 'Applied' },
    { value: 'Interviewing', label: 'Interviewing' },
    { value: 'Offer', label: 'Offer' },
    { value: 'Rejected', label: 'Rejected' },
    { value: 'Withdrawn', label: 'Withdrawn' }
  ];

  return React.createElement(Dialog, { open: true, onOpenChange: onClose },
    React.createElement(DialogContent, { className: 'max-w-2xl max-h-[90vh] overflow-y-auto' },
      React.createElement(DialogHeader, null,
        React.createElement(DialogTitle, null,
          job ? 'Edit Job Application' : 'New Job Application'
        )
      ),

      React.createElement('form', { onSubmit: handleSubmit, className: 'space-y-6' },
        React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-4' },
          // Company
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'company' }, 'Company *'),
            React.createElement(Input, {
              id: 'company',
              name: 'company',
              value: formData.company,
              onChange: handleChange,
              required: true
            })
          ),

          // Position
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'position' }, 'Position *'),
            React.createElement(Input, {
              id: 'position',
              name: 'position',
              value: formData.position,
              onChange: handleChange,
              required: true
            })
          ),

          // Location
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'location' }, 'Location'),
            React.createElement(Input, {
              id: 'location',
              name: 'location',
              value: formData.location,
              onChange: handleChange
            })
          ),

          // Status
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'status' }, 'Status'),
            React.createElement(Select, {
              value: formData.status,
              onValueChange: (value) => handleSelectChange('status', value)
            },
              React.createElement(SelectTrigger, null,
                React.createElement(SelectValue, { placeholder: 'Select status' })
              ),
              React.createElement(SelectContent, null,
                statusOptions.map(option =>
                  React.createElement(SelectItem, {
                    key: option.value,
                    value: option.value
                  }, option.label)
                )
              )
            )
          ),

          // Application Date
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'application_date' }, 'Application Date'),
            React.createElement(Input, {
              id: 'application_date',
              type: 'date',
              name: 'application_date',
              value: formData.application_date,
              onChange: handleChange
            })
          ),

          // Salary Range
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'salary_range' }, 'Salary Range'),
            React.createElement(Input, {
              id: 'salary_range',
              name: 'salary_range',
              value: formData.salary_range,
              onChange: handleChange,
              placeholder: 'e.g., $80,000 - $100,000'
            })
          ),

          // Contact Person
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'contact_person' }, 'Contact Person'),
            React.createElement(Input, {
              id: 'contact_person',
              name: 'contact_person',
              value: formData.contact_person,
              onChange: handleChange
            })
          ),

          // Contact Email
          React.createElement('div', { className: 'form-group' },
            React.createElement(Label, { htmlFor: 'contact_email' }, 'Contact Email'),
            React.createElement(Input, {
              id: 'contact_email',
              type: 'email',
              name: 'contact_email',
              value: formData.contact_email,
              onChange: handleChange
            })
          )
        ),

        // Job URL
        React.createElement('div', { className: 'form-group' },
          React.createElement(Label, { htmlFor: 'job_url' }, 'Job URL'),
          React.createElement(Input, {
            id: 'job_url',
            type: 'url',
            name: 'job_url',
            value: formData.job_url,
            onChange: handleChange,
            placeholder: 'https://company.com/jobs/123'
          })
        ),

        // Description
        React.createElement('div', { className: 'form-group' },
          React.createElement(Label, { htmlFor: 'description' }, 'Job Description'),
          React.createElement(Textarea, {
            id: 'description',
            name: 'description',
            value: formData.description,
            onChange: handleChange,
            rows: 4,
            placeholder: 'Describe the job role and requirements...'
          })
        ),

        // Notes
        React.createElement('div', { className: 'form-group' },
          React.createElement(Label, { htmlFor: 'notes' }, 'Notes'),
          React.createElement(Textarea, {
            id: 'notes',
            name: 'notes',
            value: formData.notes,
            onChange: handleChange,
            rows: 3,
            placeholder: 'Add any additional notes or comments...'
          })
        ),

        // Tags
        React.createElement('div', { className: 'form-group' },
          React.createElement(Label, null, 'Tags'),
          React.createElement('div', { className: 'flex flex-wrap gap-2 mt-2' },
            tags.map(tag =>
              React.createElement(Badge, {
                key: tag.id,
                variant: formData.tag_ids.includes(tag.id) ? 'default' : 'outline',
                className: 'cursor-pointer transition-all hover:scale-105',
                onClick: () => handleTagToggle(tag.id),
                style: formData.tag_ids.includes(tag.id) ? {
                  backgroundColor: tag.color,
                  color: 'white',
                  borderColor: tag.color
                } : {
                  backgroundColor: `${tag.color}10`,
                  color: tag.color,
                  borderColor: `${tag.color}40`
                }
              }, tag.name)
            ),
            tags.length === 0 && React.createElement('span', {
              className: 'text-sm text-muted-foreground'
            }, 'No tags available')
          )
        ),

        React.createElement(DialogFooter, { className: 'gap-3' },
          React.createElement(Button, {
            type: 'button',
            variant: 'outline',
            onClick: onClose
          }, 'Cancel'),
          React.createElement(Button, {
            type: 'submit',
            disabled: loading
          }, loading ? 'Saving...' : (job ? 'Update' : 'Create'))
        )
      )
    )
  );
};
