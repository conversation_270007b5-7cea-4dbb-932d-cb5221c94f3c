function iy(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function Lf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Df={exports:{}},xi={},Mf={exports:{}},Q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fo=Symbol.for("react.element"),ly=Symbol.for("react.portal"),ay=Symbol.for("react.fragment"),uy=Symbol.for("react.strict_mode"),cy=Symbol.for("react.profiler"),dy=Symbol.for("react.provider"),fy=Symbol.for("react.context"),py=Symbol.for("react.forward_ref"),my=Symbol.for("react.suspense"),hy=Symbol.for("react.memo"),gy=Symbol.for("react.lazy"),Mc=Symbol.iterator;function vy(e){return e===null||typeof e!="object"?null:(e=Mc&&e[Mc]||e["@@iterator"],typeof e=="function"?e:null)}var If={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ff=Object.assign,zf={};function Or(e,t,n){this.props=e,this.context=t,this.refs=zf,this.updater=n||If}Or.prototype.isReactComponent={};Or.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Or.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function $f(){}$f.prototype=Or.prototype;function su(e,t,n){this.props=e,this.context=t,this.refs=zf,this.updater=n||If}var iu=su.prototype=new $f;iu.constructor=su;Ff(iu,Or.prototype);iu.isPureReactComponent=!0;var Ic=Array.isArray,Uf=Object.prototype.hasOwnProperty,lu={current:null},Bf={key:!0,ref:!0,__self:!0,__source:!0};function Vf(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)Uf.call(t,r)&&!Bf.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Fo,type:e,key:s,ref:i,props:o,_owner:lu.current}}function yy(e,t){return{$$typeof:Fo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function au(e){return typeof e=="object"&&e!==null&&e.$$typeof===Fo}function xy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Fc=/\/+/g;function tl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?xy(""+e.key):t.toString(36)}function ws(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Fo:case ly:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+tl(i,0):r,Ic(o)?(n="",e!=null&&(n=e.replace(Fc,"$&/")+"/"),ws(o,t,n,"",function(u){return u})):o!=null&&(au(o)&&(o=yy(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Fc,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Ic(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+tl(s,l);i+=ws(s,t,n,a,o)}else if(a=vy(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+tl(s,l++),i+=ws(s,t,n,a,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function qo(e,t,n){if(e==null)return e;var r=[],o=0;return ws(e,r,"","",function(s){return t.call(n,s,o++)}),r}function wy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ze={current:null},Ss={transition:null},Sy={ReactCurrentDispatcher:ze,ReactCurrentBatchConfig:Ss,ReactCurrentOwner:lu};function Wf(){throw Error("act(...) is not supported in production builds of React.")}Q.Children={map:qo,forEach:function(e,t,n){qo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return qo(e,function(){t++}),t},toArray:function(e){return qo(e,function(t){return t})||[]},only:function(e){if(!au(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Q.Component=Or;Q.Fragment=ay;Q.Profiler=cy;Q.PureComponent=su;Q.StrictMode=uy;Q.Suspense=my;Q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Sy;Q.act=Wf;Q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ff({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=lu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Uf.call(t,a)&&!Bf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Fo,type:e.type,key:o,ref:s,props:r,_owner:i}};Q.createContext=function(e){return e={$$typeof:fy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:dy,_context:e},e.Consumer=e};Q.createElement=Vf;Q.createFactory=function(e){var t=Vf.bind(null,e);return t.type=e,t};Q.createRef=function(){return{current:null}};Q.forwardRef=function(e){return{$$typeof:py,render:e}};Q.isValidElement=au;Q.lazy=function(e){return{$$typeof:gy,_payload:{_status:-1,_result:e},_init:wy}};Q.memo=function(e,t){return{$$typeof:hy,type:e,compare:t===void 0?null:t}};Q.startTransition=function(e){var t=Ss.transition;Ss.transition={};try{e()}finally{Ss.transition=t}};Q.unstable_act=Wf;Q.useCallback=function(e,t){return ze.current.useCallback(e,t)};Q.useContext=function(e){return ze.current.useContext(e)};Q.useDebugValue=function(){};Q.useDeferredValue=function(e){return ze.current.useDeferredValue(e)};Q.useEffect=function(e,t){return ze.current.useEffect(e,t)};Q.useId=function(){return ze.current.useId()};Q.useImperativeHandle=function(e,t,n){return ze.current.useImperativeHandle(e,t,n)};Q.useInsertionEffect=function(e,t){return ze.current.useInsertionEffect(e,t)};Q.useLayoutEffect=function(e,t){return ze.current.useLayoutEffect(e,t)};Q.useMemo=function(e,t){return ze.current.useMemo(e,t)};Q.useReducer=function(e,t,n){return ze.current.useReducer(e,t,n)};Q.useRef=function(e){return ze.current.useRef(e)};Q.useState=function(e){return ze.current.useState(e)};Q.useSyncExternalStore=function(e,t,n){return ze.current.useSyncExternalStore(e,t,n)};Q.useTransition=function(){return ze.current.useTransition()};Q.version="18.3.1";Mf.exports=Q;var g=Mf.exports;const Ft=Lf(g),Hf=iy({__proto__:null,default:Ft},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ey=g,Cy=Symbol.for("react.element"),Ny=Symbol.for("react.fragment"),ky=Object.prototype.hasOwnProperty,Ty=Ey.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ry={key:!0,ref:!0,__self:!0,__source:!0};function Kf(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)ky.call(t,r)&&!Ry.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Cy,type:e,key:s,ref:i,props:o,_owner:Ty.current}}xi.Fragment=Ny;xi.jsx=Kf;xi.jsxs=Kf;Df.exports=xi;var c=Df.exports,Vl={},Qf={exports:{}},rt={},Gf={exports:{}},Xf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,A){var M=P.length;P.push(A);e:for(;0<M;){var U=M-1>>>1,J=P[U];if(0<o(J,A))P[U]=A,P[M]=J,M=U;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var A=P[0],M=P.pop();if(M!==A){P[0]=M;e:for(var U=0,J=P.length,Le=J>>>1;U<Le;){var ve=2*(U+1)-1,Nt=P[ve],De=ve+1,z=P[De];if(0>o(Nt,M))De<J&&0>o(z,Nt)?(P[U]=z,P[De]=M,U=De):(P[U]=Nt,P[ve]=M,U=ve);else if(De<J&&0>o(z,M))P[U]=z,P[De]=M,U=De;else break e}}return A}function o(P,A){var M=P.sortIndex-A.sortIndex;return M!==0?M:P.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var a=[],u=[],f=1,m=null,y=3,w=!1,S=!1,p=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=P)r(u),A.sortIndex=A.expirationTime,t(a,A);else break;A=n(u)}}function E(P){if(p=!1,v(P),!S)if(n(a)!==null)S=!0,F(C);else{var A=n(u);A!==null&&W(E,A.startTime-P)}}function C(P,A){S=!1,p&&(p=!1,h(R),R=-1),w=!0;var M=y;try{for(v(A),m=n(a);m!==null&&(!(m.expirationTime>A)||P&&!I());){var U=m.callback;if(typeof U=="function"){m.callback=null,y=m.priorityLevel;var J=U(m.expirationTime<=A);A=e.unstable_now(),typeof J=="function"?m.callback=J:m===n(a)&&r(a),v(A)}else r(a);m=n(a)}if(m!==null)var Le=!0;else{var ve=n(u);ve!==null&&W(E,ve.startTime-A),Le=!1}return Le}finally{m=null,y=M,w=!1}}var T=!1,N=null,R=-1,j=5,_=-1;function I(){return!(e.unstable_now()-_<j)}function L(){if(N!==null){var P=e.unstable_now();_=P;var A=!0;try{A=N(!0,P)}finally{A?B():(T=!1,N=null)}}else T=!1}var B;if(typeof d=="function")B=function(){d(L)};else if(typeof MessageChannel<"u"){var O=new MessageChannel,$=O.port2;O.port1.onmessage=L,B=function(){$.postMessage(null)}}else B=function(){x(L,0)};function F(P){N=P,T||(T=!0,B())}function W(P,A){R=x(function(){P(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){S||w||(S=!0,F(C))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(P){switch(y){case 1:case 2:case 3:var A=3;break;default:A=y}var M=y;y=A;try{return P()}finally{y=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,A){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var M=y;y=P;try{return A()}finally{y=M}},e.unstable_scheduleCallback=function(P,A,M){var U=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?U+M:U):M=U,P){case 1:var J=-1;break;case 2:J=250;break;case 5:J=**********;break;case 4:J=1e4;break;default:J=5e3}return J=M+J,P={id:f++,callback:A,priorityLevel:P,startTime:M,expirationTime:J,sortIndex:-1},M>U?(P.sortIndex=M,t(u,P),n(a)===null&&P===n(u)&&(p?(h(R),R=-1):p=!0,W(E,M-U))):(P.sortIndex=J,t(a,P),S||w||(S=!0,F(C))),P},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(P){var A=y;return function(){var M=y;y=A;try{return P.apply(this,arguments)}finally{y=M}}}})(Xf);Gf.exports=Xf;var Py=Gf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var by=g,nt=Py;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var qf=new Set,yo={};function Gn(e,t){Nr(e,t),Nr(e+"Capture",t)}function Nr(e,t){for(yo[e]=t,e=0;e<t.length;e++)qf.add(t[e])}var Kt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Wl=Object.prototype.hasOwnProperty,_y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,zc={},$c={};function jy(e){return Wl.call($c,e)?!0:Wl.call(zc,e)?!1:_y.test(e)?$c[e]=!0:(zc[e]=!0,!1)}function Ay(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Oy(e,t,n,r){if(t===null||typeof t>"u"||Ay(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $e(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Te={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Te[e]=new $e(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Te[t]=new $e(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Te[e]=new $e(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Te[e]=new $e(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Te[e]=new $e(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Te[e]=new $e(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Te[e]=new $e(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Te[e]=new $e(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Te[e]=new $e(e,5,!1,e.toLowerCase(),null,!1,!1)});var uu=/[\-:]([a-z])/g;function cu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(uu,cu);Te[t]=new $e(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(uu,cu);Te[t]=new $e(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(uu,cu);Te[t]=new $e(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Te[e]=new $e(e,1,!1,e.toLowerCase(),null,!1,!1)});Te.xlinkHref=new $e("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Te[e]=new $e(e,1,!1,e.toLowerCase(),null,!0,!0)});function du(e,t,n,r){var o=Te.hasOwnProperty(t)?Te[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Oy(t,n,o,r)&&(n=null),r||o===null?jy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Jt=by.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Yo=Symbol.for("react.element"),nr=Symbol.for("react.portal"),rr=Symbol.for("react.fragment"),fu=Symbol.for("react.strict_mode"),Hl=Symbol.for("react.profiler"),Yf=Symbol.for("react.provider"),Jf=Symbol.for("react.context"),pu=Symbol.for("react.forward_ref"),Kl=Symbol.for("react.suspense"),Ql=Symbol.for("react.suspense_list"),mu=Symbol.for("react.memo"),rn=Symbol.for("react.lazy"),Zf=Symbol.for("react.offscreen"),Uc=Symbol.iterator;function Hr(e){return e===null||typeof e!="object"?null:(e=Uc&&e[Uc]||e["@@iterator"],typeof e=="function"?e:null)}var de=Object.assign,nl;function no(e){if(nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);nl=t&&t[1]||""}return`
`+nl+e}var rl=!1;function ol(e,t){if(!e||rl)return"";rl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,l=s.length-1;1<=i&&0<=l&&o[i]!==s[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==s[l]){if(i!==1||l!==1)do if(i--,l--,0>l||o[i]!==s[l]){var a=`
`+o[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=l);break}}}finally{rl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?no(e):""}function Ly(e){switch(e.tag){case 5:return no(e.type);case 16:return no("Lazy");case 13:return no("Suspense");case 19:return no("SuspenseList");case 0:case 2:case 15:return e=ol(e.type,!1),e;case 11:return e=ol(e.type.render,!1),e;case 1:return e=ol(e.type,!0),e;default:return""}}function Gl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case rr:return"Fragment";case nr:return"Portal";case Hl:return"Profiler";case fu:return"StrictMode";case Kl:return"Suspense";case Ql:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Jf:return(e.displayName||"Context")+".Consumer";case Yf:return(e._context.displayName||"Context")+".Provider";case pu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case mu:return t=e.displayName||null,t!==null?t:Gl(e.type)||"Memo";case rn:t=e._payload,e=e._init;try{return Gl(e(t))}catch{}}return null}function Dy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Gl(t);case 8:return t===fu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function yn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ep(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function My(e){var t=ep(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Jo(e){e._valueTracker||(e._valueTracker=My(e))}function tp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ep(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function $s(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Xl(e,t){var n=t.checked;return de({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Bc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=yn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function np(e,t){t=t.checked,t!=null&&du(e,"checked",t,!1)}function ql(e,t){np(e,t);var n=yn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Yl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Yl(e,t.type,yn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Vc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Yl(e,t,n){(t!=="number"||$s(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ro=Array.isArray;function mr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+yn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Jl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return de({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Wc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(ro(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:yn(n)}}function rp(e,t){var n=yn(t.value),r=yn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Hc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function op(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Zl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?op(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Zo,sp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Zo=Zo||document.createElement("div"),Zo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Zo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function xo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var lo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Iy=["Webkit","ms","Moz","O"];Object.keys(lo).forEach(function(e){Iy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),lo[t]=lo[e]})});function ip(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||lo.hasOwnProperty(e)&&lo[e]?(""+t).trim():t+"px"}function lp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=ip(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Fy=de({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ea(e,t){if(t){if(Fy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function ta(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var na=null;function hu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ra=null,hr=null,gr=null;function Kc(e){if(e=Uo(e)){if(typeof ra!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Ni(t),ra(e.stateNode,e.type,t))}}function ap(e){hr?gr?gr.push(e):gr=[e]:hr=e}function up(){if(hr){var e=hr,t=gr;if(gr=hr=null,Kc(e),t)for(e=0;e<t.length;e++)Kc(t[e])}}function cp(e,t){return e(t)}function dp(){}var sl=!1;function fp(e,t,n){if(sl)return e(t,n);sl=!0;try{return cp(e,t,n)}finally{sl=!1,(hr!==null||gr!==null)&&(dp(),up())}}function wo(e,t){var n=e.stateNode;if(n===null)return null;var r=Ni(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var oa=!1;if(Kt)try{var Kr={};Object.defineProperty(Kr,"passive",{get:function(){oa=!0}}),window.addEventListener("test",Kr,Kr),window.removeEventListener("test",Kr,Kr)}catch{oa=!1}function zy(e,t,n,r,o,s,i,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var ao=!1,Us=null,Bs=!1,sa=null,$y={onError:function(e){ao=!0,Us=e}};function Uy(e,t,n,r,o,s,i,l,a){ao=!1,Us=null,zy.apply($y,arguments)}function By(e,t,n,r,o,s,i,l,a){if(Uy.apply(this,arguments),ao){if(ao){var u=Us;ao=!1,Us=null}else throw Error(b(198));Bs||(Bs=!0,sa=u)}}function Xn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function pp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Qc(e){if(Xn(e)!==e)throw Error(b(188))}function Vy(e){var t=e.alternate;if(!t){if(t=Xn(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return Qc(o),e;if(s===r)return Qc(o),t;s=s.sibling}throw Error(b(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=s;break}if(l===r){i=!0,r=o,n=s;break}l=l.sibling}if(!i){for(l=s.child;l;){if(l===n){i=!0,n=s,r=o;break}if(l===r){i=!0,r=s,n=o;break}l=l.sibling}if(!i)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function mp(e){return e=Vy(e),e!==null?hp(e):null}function hp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=hp(e);if(t!==null)return t;e=e.sibling}return null}var gp=nt.unstable_scheduleCallback,Gc=nt.unstable_cancelCallback,Wy=nt.unstable_shouldYield,Hy=nt.unstable_requestPaint,me=nt.unstable_now,Ky=nt.unstable_getCurrentPriorityLevel,gu=nt.unstable_ImmediatePriority,vp=nt.unstable_UserBlockingPriority,Vs=nt.unstable_NormalPriority,Qy=nt.unstable_LowPriority,yp=nt.unstable_IdlePriority,wi=null,At=null;function Gy(e){if(At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(wi,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:Yy,Xy=Math.log,qy=Math.LN2;function Yy(e){return e>>>=0,e===0?32:31-(Xy(e)/qy|0)|0}var es=64,ts=4194304;function oo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ws(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var l=i&~o;l!==0?r=oo(l):(s&=i,s!==0&&(r=oo(s)))}else i=n&~o,i!==0?r=oo(i):s!==0&&(r=oo(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-gt(t),o=1<<n,r|=e[n],t&=~o;return r}function Jy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-gt(s),l=1<<i,a=o[i];a===-1?(!(l&n)||l&r)&&(o[i]=Jy(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function ia(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function xp(){var e=es;return es<<=1,!(es&4194240)&&(es=64),e}function il(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function zo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=n}function ex(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-gt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function vu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-gt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Y=0;function wp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Sp,yu,Ep,Cp,Np,la=!1,ns=[],cn=null,dn=null,fn=null,So=new Map,Eo=new Map,sn=[],tx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Xc(e,t){switch(e){case"focusin":case"focusout":cn=null;break;case"dragenter":case"dragleave":dn=null;break;case"mouseover":case"mouseout":fn=null;break;case"pointerover":case"pointerout":So.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Eo.delete(t.pointerId)}}function Qr(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=Uo(t),t!==null&&yu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function nx(e,t,n,r,o){switch(t){case"focusin":return cn=Qr(cn,e,t,n,r,o),!0;case"dragenter":return dn=Qr(dn,e,t,n,r,o),!0;case"mouseover":return fn=Qr(fn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return So.set(s,Qr(So.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Eo.set(s,Qr(Eo.get(s)||null,e,t,n,r,o)),!0}return!1}function kp(e){var t=jn(e.target);if(t!==null){var n=Xn(t);if(n!==null){if(t=n.tag,t===13){if(t=pp(n),t!==null){e.blockedOn=t,Np(e.priority,function(){Ep(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Es(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=aa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);na=r,n.target.dispatchEvent(r),na=null}else return t=Uo(n),t!==null&&yu(t),e.blockedOn=n,!1;t.shift()}return!0}function qc(e,t,n){Es(e)&&n.delete(t)}function rx(){la=!1,cn!==null&&Es(cn)&&(cn=null),dn!==null&&Es(dn)&&(dn=null),fn!==null&&Es(fn)&&(fn=null),So.forEach(qc),Eo.forEach(qc)}function Gr(e,t){e.blockedOn===t&&(e.blockedOn=null,la||(la=!0,nt.unstable_scheduleCallback(nt.unstable_NormalPriority,rx)))}function Co(e){function t(o){return Gr(o,e)}if(0<ns.length){Gr(ns[0],e);for(var n=1;n<ns.length;n++){var r=ns[n];r.blockedOn===e&&(r.blockedOn=null)}}for(cn!==null&&Gr(cn,e),dn!==null&&Gr(dn,e),fn!==null&&Gr(fn,e),So.forEach(t),Eo.forEach(t),n=0;n<sn.length;n++)r=sn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<sn.length&&(n=sn[0],n.blockedOn===null);)kp(n),n.blockedOn===null&&sn.shift()}var vr=Jt.ReactCurrentBatchConfig,Hs=!0;function ox(e,t,n,r){var o=Y,s=vr.transition;vr.transition=null;try{Y=1,xu(e,t,n,r)}finally{Y=o,vr.transition=s}}function sx(e,t,n,r){var o=Y,s=vr.transition;vr.transition=null;try{Y=4,xu(e,t,n,r)}finally{Y=o,vr.transition=s}}function xu(e,t,n,r){if(Hs){var o=aa(e,t,n,r);if(o===null)gl(e,t,r,Ks,n),Xc(e,r);else if(nx(o,e,t,n,r))r.stopPropagation();else if(Xc(e,r),t&4&&-1<tx.indexOf(e)){for(;o!==null;){var s=Uo(o);if(s!==null&&Sp(s),s=aa(e,t,n,r),s===null&&gl(e,t,r,Ks,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else gl(e,t,r,null,n)}}var Ks=null;function aa(e,t,n,r){if(Ks=null,e=hu(r),e=jn(e),e!==null)if(t=Xn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=pp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ks=e,null}function Tp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ky()){case gu:return 1;case vp:return 4;case Vs:case Qy:return 16;case yp:return 536870912;default:return 16}default:return 16}}var an=null,wu=null,Cs=null;function Rp(){if(Cs)return Cs;var e,t=wu,n=t.length,r,o="value"in an?an.value:an.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Cs=o.slice(e,1<r?1-r:void 0)}function Ns(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function rs(){return!0}function Yc(){return!1}function ot(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?rs:Yc,this.isPropagationStopped=Yc,this}return de(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=rs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=rs)},persist:function(){},isPersistent:rs}),t}var Lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Su=ot(Lr),$o=de({},Lr,{view:0,detail:0}),ix=ot($o),ll,al,Xr,Si=de({},$o,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Eu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xr&&(Xr&&e.type==="mousemove"?(ll=e.screenX-Xr.screenX,al=e.screenY-Xr.screenY):al=ll=0,Xr=e),ll)},movementY:function(e){return"movementY"in e?e.movementY:al}}),Jc=ot(Si),lx=de({},Si,{dataTransfer:0}),ax=ot(lx),ux=de({},$o,{relatedTarget:0}),ul=ot(ux),cx=de({},Lr,{animationName:0,elapsedTime:0,pseudoElement:0}),dx=ot(cx),fx=de({},Lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),px=ot(fx),mx=de({},Lr,{data:0}),Zc=ot(mx),hx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vx[e])?!!t[e]:!1}function Eu(){return yx}var xx=de({},$o,{key:function(e){if(e.key){var t=hx[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ns(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Eu,charCode:function(e){return e.type==="keypress"?Ns(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ns(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),wx=ot(xx),Sx=de({},Si,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ed=ot(Sx),Ex=de({},$o,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Eu}),Cx=ot(Ex),Nx=de({},Lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),kx=ot(Nx),Tx=de({},Si,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rx=ot(Tx),Px=[9,13,27,32],Cu=Kt&&"CompositionEvent"in window,uo=null;Kt&&"documentMode"in document&&(uo=document.documentMode);var bx=Kt&&"TextEvent"in window&&!uo,Pp=Kt&&(!Cu||uo&&8<uo&&11>=uo),td=" ",nd=!1;function bp(e,t){switch(e){case"keyup":return Px.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _p(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var or=!1;function _x(e,t){switch(e){case"compositionend":return _p(t);case"keypress":return t.which!==32?null:(nd=!0,td);case"textInput":return e=t.data,e===td&&nd?null:e;default:return null}}function jx(e,t){if(or)return e==="compositionend"||!Cu&&bp(e,t)?(e=Rp(),Cs=wu=an=null,or=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pp&&t.locale!=="ko"?null:t.data;default:return null}}var Ax={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function rd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ax[e.type]:t==="textarea"}function jp(e,t,n,r){ap(r),t=Qs(t,"onChange"),0<t.length&&(n=new Su("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var co=null,No=null;function Ox(e){Bp(e,0)}function Ei(e){var t=lr(e);if(tp(t))return e}function Lx(e,t){if(e==="change")return t}var Ap=!1;if(Kt){var cl;if(Kt){var dl="oninput"in document;if(!dl){var od=document.createElement("div");od.setAttribute("oninput","return;"),dl=typeof od.oninput=="function"}cl=dl}else cl=!1;Ap=cl&&(!document.documentMode||9<document.documentMode)}function sd(){co&&(co.detachEvent("onpropertychange",Op),No=co=null)}function Op(e){if(e.propertyName==="value"&&Ei(No)){var t=[];jp(t,No,e,hu(e)),fp(Ox,t)}}function Dx(e,t,n){e==="focusin"?(sd(),co=t,No=n,co.attachEvent("onpropertychange",Op)):e==="focusout"&&sd()}function Mx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ei(No)}function Ix(e,t){if(e==="click")return Ei(t)}function Fx(e,t){if(e==="input"||e==="change")return Ei(t)}function zx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:zx;function ko(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Wl.call(t,o)||!yt(e[o],t[o]))return!1}return!0}function id(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ld(e,t){var n=id(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=id(n)}}function Lp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Lp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Dp(){for(var e=window,t=$s();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=$s(e.document)}return t}function Nu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function $x(e){var t=Dp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Lp(n.ownerDocument.documentElement,n)){if(r!==null&&Nu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=ld(n,s);var i=ld(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ux=Kt&&"documentMode"in document&&11>=document.documentMode,sr=null,ua=null,fo=null,ca=!1;function ad(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ca||sr==null||sr!==$s(r)||(r=sr,"selectionStart"in r&&Nu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),fo&&ko(fo,r)||(fo=r,r=Qs(ua,"onSelect"),0<r.length&&(t=new Su("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=sr)))}function os(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ir={animationend:os("Animation","AnimationEnd"),animationiteration:os("Animation","AnimationIteration"),animationstart:os("Animation","AnimationStart"),transitionend:os("Transition","TransitionEnd")},fl={},Mp={};Kt&&(Mp=document.createElement("div").style,"AnimationEvent"in window||(delete ir.animationend.animation,delete ir.animationiteration.animation,delete ir.animationstart.animation),"TransitionEvent"in window||delete ir.transitionend.transition);function Ci(e){if(fl[e])return fl[e];if(!ir[e])return e;var t=ir[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Mp)return fl[e]=t[n];return e}var Ip=Ci("animationend"),Fp=Ci("animationiteration"),zp=Ci("animationstart"),$p=Ci("transitionend"),Up=new Map,ud="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function En(e,t){Up.set(e,t),Gn(t,[e])}for(var pl=0;pl<ud.length;pl++){var ml=ud[pl],Bx=ml.toLowerCase(),Vx=ml[0].toUpperCase()+ml.slice(1);En(Bx,"on"+Vx)}En(Ip,"onAnimationEnd");En(Fp,"onAnimationIteration");En(zp,"onAnimationStart");En("dblclick","onDoubleClick");En("focusin","onFocus");En("focusout","onBlur");En($p,"onTransitionEnd");Nr("onMouseEnter",["mouseout","mouseover"]);Nr("onMouseLeave",["mouseout","mouseover"]);Nr("onPointerEnter",["pointerout","pointerover"]);Nr("onPointerLeave",["pointerout","pointerover"]);Gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Gn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var so="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wx=new Set("cancel close invalid load scroll toggle".split(" ").concat(so));function cd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,By(r,t,void 0,e),e.currentTarget=null}function Bp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&o.isPropagationStopped())break e;cd(o,l,u),s=a}else for(i=0;i<r.length;i++){if(l=r[i],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&o.isPropagationStopped())break e;cd(o,l,u),s=a}}}if(Bs)throw e=sa,Bs=!1,sa=null,e}function ie(e,t){var n=t[ha];n===void 0&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Vp(t,e,2,!1),n.add(r))}function hl(e,t,n){var r=0;t&&(r|=4),Vp(n,e,r,t)}var ss="_reactListening"+Math.random().toString(36).slice(2);function To(e){if(!e[ss]){e[ss]=!0,qf.forEach(function(n){n!=="selectionchange"&&(Wx.has(n)||hl(n,!1,e),hl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ss]||(t[ss]=!0,hl("selectionchange",!1,t))}}function Vp(e,t,n,r){switch(Tp(t)){case 1:var o=ox;break;case 4:o=sx;break;default:o=xu}n=o.bind(null,t,n,e),o=void 0,!oa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function gl(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;i=i.return}for(;l!==null;){if(i=jn(l),i===null)return;if(a=i.tag,a===5||a===6){r=s=i;continue e}l=l.parentNode}}r=r.return}fp(function(){var u=s,f=hu(n),m=[];e:{var y=Up.get(e);if(y!==void 0){var w=Su,S=e;switch(e){case"keypress":if(Ns(n)===0)break e;case"keydown":case"keyup":w=wx;break;case"focusin":S="focus",w=ul;break;case"focusout":S="blur",w=ul;break;case"beforeblur":case"afterblur":w=ul;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Jc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=ax;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Cx;break;case Ip:case Fp:case zp:w=dx;break;case $p:w=kx;break;case"scroll":w=ix;break;case"wheel":w=Rx;break;case"copy":case"cut":case"paste":w=px;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=ed}var p=(t&4)!==0,x=!p&&e==="scroll",h=p?y!==null?y+"Capture":null:y;p=[];for(var d=u,v;d!==null;){v=d;var E=v.stateNode;if(v.tag===5&&E!==null&&(v=E,h!==null&&(E=wo(d,h),E!=null&&p.push(Ro(d,E,v)))),x)break;d=d.return}0<p.length&&(y=new w(y,S,null,n,f),m.push({event:y,listeners:p}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",y&&n!==na&&(S=n.relatedTarget||n.fromElement)&&(jn(S)||S[Qt]))break e;if((w||y)&&(y=f.window===f?f:(y=f.ownerDocument)?y.defaultView||y.parentWindow:window,w?(S=n.relatedTarget||n.toElement,w=u,S=S?jn(S):null,S!==null&&(x=Xn(S),S!==x||S.tag!==5&&S.tag!==6)&&(S=null)):(w=null,S=u),w!==S)){if(p=Jc,E="onMouseLeave",h="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(p=ed,E="onPointerLeave",h="onPointerEnter",d="pointer"),x=w==null?y:lr(w),v=S==null?y:lr(S),y=new p(E,d+"leave",w,n,f),y.target=x,y.relatedTarget=v,E=null,jn(f)===u&&(p=new p(h,d+"enter",S,n,f),p.target=v,p.relatedTarget=x,E=p),x=E,w&&S)t:{for(p=w,h=S,d=0,v=p;v;v=Yn(v))d++;for(v=0,E=h;E;E=Yn(E))v++;for(;0<d-v;)p=Yn(p),d--;for(;0<v-d;)h=Yn(h),v--;for(;d--;){if(p===h||h!==null&&p===h.alternate)break t;p=Yn(p),h=Yn(h)}p=null}else p=null;w!==null&&dd(m,y,w,p,!1),S!==null&&x!==null&&dd(m,x,S,p,!0)}}e:{if(y=u?lr(u):window,w=y.nodeName&&y.nodeName.toLowerCase(),w==="select"||w==="input"&&y.type==="file")var C=Lx;else if(rd(y))if(Ap)C=Fx;else{C=Mx;var T=Dx}else(w=y.nodeName)&&w.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(C=Ix);if(C&&(C=C(e,u))){jp(m,C,n,f);break e}T&&T(e,y,u),e==="focusout"&&(T=y._wrapperState)&&T.controlled&&y.type==="number"&&Yl(y,"number",y.value)}switch(T=u?lr(u):window,e){case"focusin":(rd(T)||T.contentEditable==="true")&&(sr=T,ua=u,fo=null);break;case"focusout":fo=ua=sr=null;break;case"mousedown":ca=!0;break;case"contextmenu":case"mouseup":case"dragend":ca=!1,ad(m,n,f);break;case"selectionchange":if(Ux)break;case"keydown":case"keyup":ad(m,n,f)}var N;if(Cu)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else or?bp(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Pp&&n.locale!=="ko"&&(or||R!=="onCompositionStart"?R==="onCompositionEnd"&&or&&(N=Rp()):(an=f,wu="value"in an?an.value:an.textContent,or=!0)),T=Qs(u,R),0<T.length&&(R=new Zc(R,e,null,n,f),m.push({event:R,listeners:T}),N?R.data=N:(N=_p(n),N!==null&&(R.data=N)))),(N=bx?_x(e,n):jx(e,n))&&(u=Qs(u,"onBeforeInput"),0<u.length&&(f=new Zc("onBeforeInput","beforeinput",null,n,f),m.push({event:f,listeners:u}),f.data=N))}Bp(m,t)})}function Ro(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qs(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=wo(e,n),s!=null&&r.unshift(Ro(e,s,o)),s=wo(e,t),s!=null&&r.push(Ro(e,s,o))),e=e.return}return r}function Yn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function dd(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=wo(n,s),a!=null&&i.unshift(Ro(n,a,l))):o||(a=wo(n,s),a!=null&&i.push(Ro(n,a,l)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Hx=/\r\n?/g,Kx=/\u0000|\uFFFD/g;function fd(e){return(typeof e=="string"?e:""+e).replace(Hx,`
`).replace(Kx,"")}function is(e,t,n){if(t=fd(t),fd(e)!==t&&n)throw Error(b(425))}function Gs(){}var da=null,fa=null;function pa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ma=typeof setTimeout=="function"?setTimeout:void 0,Qx=typeof clearTimeout=="function"?clearTimeout:void 0,pd=typeof Promise=="function"?Promise:void 0,Gx=typeof queueMicrotask=="function"?queueMicrotask:typeof pd<"u"?function(e){return pd.resolve(null).then(e).catch(Xx)}:ma;function Xx(e){setTimeout(function(){throw e})}function vl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Co(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Co(t)}function pn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function md(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Dr=Math.random().toString(36).slice(2),bt="__reactFiber$"+Dr,Po="__reactProps$"+Dr,Qt="__reactContainer$"+Dr,ha="__reactEvents$"+Dr,qx="__reactListeners$"+Dr,Yx="__reactHandles$"+Dr;function jn(e){var t=e[bt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Qt]||n[bt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=md(e);e!==null;){if(n=e[bt])return n;e=md(e)}return t}e=n,n=e.parentNode}return null}function Uo(e){return e=e[bt]||e[Qt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Ni(e){return e[Po]||null}var ga=[],ar=-1;function Cn(e){return{current:e}}function le(e){0>ar||(e.current=ga[ar],ga[ar]=null,ar--)}function ne(e,t){ar++,ga[ar]=e.current,e.current=t}var xn={},Oe=Cn(xn),Ve=Cn(!1),Fn=xn;function kr(e,t){var n=e.type.contextTypes;if(!n)return xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function We(e){return e=e.childContextTypes,e!=null}function Xs(){le(Ve),le(Oe)}function hd(e,t,n){if(Oe.current!==xn)throw Error(b(168));ne(Oe,t),ne(Ve,n)}function Wp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(b(108,Dy(e)||"Unknown",o));return de({},n,r)}function qs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||xn,Fn=Oe.current,ne(Oe,e),ne(Ve,Ve.current),!0}function gd(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=Wp(e,t,Fn),r.__reactInternalMemoizedMergedChildContext=e,le(Ve),le(Oe),ne(Oe,e)):le(Ve),ne(Ve,n)}var $t=null,ki=!1,yl=!1;function Hp(e){$t===null?$t=[e]:$t.push(e)}function Jx(e){ki=!0,Hp(e)}function Nn(){if(!yl&&$t!==null){yl=!0;var e=0,t=Y;try{var n=$t;for(Y=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}$t=null,ki=!1}catch(o){throw $t!==null&&($t=$t.slice(e+1)),gp(gu,Nn),o}finally{Y=t,yl=!1}}return null}var ur=[],cr=0,Ys=null,Js=0,st=[],it=0,zn=null,Ut=1,Bt="";function Rn(e,t){ur[cr++]=Js,ur[cr++]=Ys,Ys=e,Js=t}function Kp(e,t,n){st[it++]=Ut,st[it++]=Bt,st[it++]=zn,zn=e;var r=Ut;e=Bt;var o=32-gt(r)-1;r&=~(1<<o),n+=1;var s=32-gt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Ut=1<<32-gt(t)+o|n<<o|r,Bt=s+e}else Ut=1<<s|n<<o|r,Bt=e}function ku(e){e.return!==null&&(Rn(e,1),Kp(e,1,0))}function Tu(e){for(;e===Ys;)Ys=ur[--cr],ur[cr]=null,Js=ur[--cr],ur[cr]=null;for(;e===zn;)zn=st[--it],st[it]=null,Bt=st[--it],st[it]=null,Ut=st[--it],st[it]=null}var et=null,Ze=null,ae=!1,ht=null;function Qp(e,t){var n=lt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function vd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,et=e,Ze=pn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,et=e,Ze=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=zn!==null?{id:Ut,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=lt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,et=e,Ze=null,!0):!1;default:return!1}}function va(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ya(e){if(ae){var t=Ze;if(t){var n=t;if(!vd(e,t)){if(va(e))throw Error(b(418));t=pn(n.nextSibling);var r=et;t&&vd(e,t)?Qp(r,n):(e.flags=e.flags&-4097|2,ae=!1,et=e)}}else{if(va(e))throw Error(b(418));e.flags=e.flags&-4097|2,ae=!1,et=e}}}function yd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;et=e}function ls(e){if(e!==et)return!1;if(!ae)return yd(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!pa(e.type,e.memoizedProps)),t&&(t=Ze)){if(va(e))throw Gp(),Error(b(418));for(;t;)Qp(e,t),t=pn(t.nextSibling)}if(yd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ze=pn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ze=null}}else Ze=et?pn(e.stateNode.nextSibling):null;return!0}function Gp(){for(var e=Ze;e;)e=pn(e.nextSibling)}function Tr(){Ze=et=null,ae=!1}function Ru(e){ht===null?ht=[e]:ht.push(e)}var Zx=Jt.ReactCurrentBatchConfig;function qr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var l=o.refs;i===null?delete l[s]:l[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function as(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xd(e){var t=e._init;return t(e._payload)}function Xp(e){function t(h,d){if(e){var v=h.deletions;v===null?(h.deletions=[d],h.flags|=16):v.push(d)}}function n(h,d){if(!e)return null;for(;d!==null;)t(h,d),d=d.sibling;return null}function r(h,d){for(h=new Map;d!==null;)d.key!==null?h.set(d.key,d):h.set(d.index,d),d=d.sibling;return h}function o(h,d){return h=vn(h,d),h.index=0,h.sibling=null,h}function s(h,d,v){return h.index=v,e?(v=h.alternate,v!==null?(v=v.index,v<d?(h.flags|=2,d):v):(h.flags|=2,d)):(h.flags|=1048576,d)}function i(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,d,v,E){return d===null||d.tag!==6?(d=kl(v,h.mode,E),d.return=h,d):(d=o(d,v),d.return=h,d)}function a(h,d,v,E){var C=v.type;return C===rr?f(h,d,v.props.children,E,v.key):d!==null&&(d.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===rn&&xd(C)===d.type)?(E=o(d,v.props),E.ref=qr(h,d,v),E.return=h,E):(E=js(v.type,v.key,v.props,null,h.mode,E),E.ref=qr(h,d,v),E.return=h,E)}function u(h,d,v,E){return d===null||d.tag!==4||d.stateNode.containerInfo!==v.containerInfo||d.stateNode.implementation!==v.implementation?(d=Tl(v,h.mode,E),d.return=h,d):(d=o(d,v.children||[]),d.return=h,d)}function f(h,d,v,E,C){return d===null||d.tag!==7?(d=Mn(v,h.mode,E,C),d.return=h,d):(d=o(d,v),d.return=h,d)}function m(h,d,v){if(typeof d=="string"&&d!==""||typeof d=="number")return d=kl(""+d,h.mode,v),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Yo:return v=js(d.type,d.key,d.props,null,h.mode,v),v.ref=qr(h,null,d),v.return=h,v;case nr:return d=Tl(d,h.mode,v),d.return=h,d;case rn:var E=d._init;return m(h,E(d._payload),v)}if(ro(d)||Hr(d))return d=Mn(d,h.mode,v,null),d.return=h,d;as(h,d)}return null}function y(h,d,v,E){var C=d!==null?d.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:l(h,d,""+v,E);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Yo:return v.key===C?a(h,d,v,E):null;case nr:return v.key===C?u(h,d,v,E):null;case rn:return C=v._init,y(h,d,C(v._payload),E)}if(ro(v)||Hr(v))return C!==null?null:f(h,d,v,E,null);as(h,v)}return null}function w(h,d,v,E,C){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(v)||null,l(d,h,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Yo:return h=h.get(E.key===null?v:E.key)||null,a(d,h,E,C);case nr:return h=h.get(E.key===null?v:E.key)||null,u(d,h,E,C);case rn:var T=E._init;return w(h,d,v,T(E._payload),C)}if(ro(E)||Hr(E))return h=h.get(v)||null,f(d,h,E,C,null);as(d,E)}return null}function S(h,d,v,E){for(var C=null,T=null,N=d,R=d=0,j=null;N!==null&&R<v.length;R++){N.index>R?(j=N,N=null):j=N.sibling;var _=y(h,N,v[R],E);if(_===null){N===null&&(N=j);break}e&&N&&_.alternate===null&&t(h,N),d=s(_,d,R),T===null?C=_:T.sibling=_,T=_,N=j}if(R===v.length)return n(h,N),ae&&Rn(h,R),C;if(N===null){for(;R<v.length;R++)N=m(h,v[R],E),N!==null&&(d=s(N,d,R),T===null?C=N:T.sibling=N,T=N);return ae&&Rn(h,R),C}for(N=r(h,N);R<v.length;R++)j=w(N,h,R,v[R],E),j!==null&&(e&&j.alternate!==null&&N.delete(j.key===null?R:j.key),d=s(j,d,R),T===null?C=j:T.sibling=j,T=j);return e&&N.forEach(function(I){return t(h,I)}),ae&&Rn(h,R),C}function p(h,d,v,E){var C=Hr(v);if(typeof C!="function")throw Error(b(150));if(v=C.call(v),v==null)throw Error(b(151));for(var T=C=null,N=d,R=d=0,j=null,_=v.next();N!==null&&!_.done;R++,_=v.next()){N.index>R?(j=N,N=null):j=N.sibling;var I=y(h,N,_.value,E);if(I===null){N===null&&(N=j);break}e&&N&&I.alternate===null&&t(h,N),d=s(I,d,R),T===null?C=I:T.sibling=I,T=I,N=j}if(_.done)return n(h,N),ae&&Rn(h,R),C;if(N===null){for(;!_.done;R++,_=v.next())_=m(h,_.value,E),_!==null&&(d=s(_,d,R),T===null?C=_:T.sibling=_,T=_);return ae&&Rn(h,R),C}for(N=r(h,N);!_.done;R++,_=v.next())_=w(N,h,R,_.value,E),_!==null&&(e&&_.alternate!==null&&N.delete(_.key===null?R:_.key),d=s(_,d,R),T===null?C=_:T.sibling=_,T=_);return e&&N.forEach(function(L){return t(h,L)}),ae&&Rn(h,R),C}function x(h,d,v,E){if(typeof v=="object"&&v!==null&&v.type===rr&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Yo:e:{for(var C=v.key,T=d;T!==null;){if(T.key===C){if(C=v.type,C===rr){if(T.tag===7){n(h,T.sibling),d=o(T,v.props.children),d.return=h,h=d;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===rn&&xd(C)===T.type){n(h,T.sibling),d=o(T,v.props),d.ref=qr(h,T,v),d.return=h,h=d;break e}n(h,T);break}else t(h,T);T=T.sibling}v.type===rr?(d=Mn(v.props.children,h.mode,E,v.key),d.return=h,h=d):(E=js(v.type,v.key,v.props,null,h.mode,E),E.ref=qr(h,d,v),E.return=h,h=E)}return i(h);case nr:e:{for(T=v.key;d!==null;){if(d.key===T)if(d.tag===4&&d.stateNode.containerInfo===v.containerInfo&&d.stateNode.implementation===v.implementation){n(h,d.sibling),d=o(d,v.children||[]),d.return=h,h=d;break e}else{n(h,d);break}else t(h,d);d=d.sibling}d=Tl(v,h.mode,E),d.return=h,h=d}return i(h);case rn:return T=v._init,x(h,d,T(v._payload),E)}if(ro(v))return S(h,d,v,E);if(Hr(v))return p(h,d,v,E);as(h,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,d!==null&&d.tag===6?(n(h,d.sibling),d=o(d,v),d.return=h,h=d):(n(h,d),d=kl(v,h.mode,E),d.return=h,h=d),i(h)):n(h,d)}return x}var Rr=Xp(!0),qp=Xp(!1),Zs=Cn(null),ei=null,dr=null,Pu=null;function bu(){Pu=dr=ei=null}function _u(e){var t=Zs.current;le(Zs),e._currentValue=t}function xa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function yr(e,t){ei=e,Pu=dr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Be=!0),e.firstContext=null)}function ut(e){var t=e._currentValue;if(Pu!==e)if(e={context:e,memoizedValue:t,next:null},dr===null){if(ei===null)throw Error(b(308));dr=e,ei.dependencies={lanes:0,firstContext:e}}else dr=dr.next=e;return t}var An=null;function ju(e){An===null?An=[e]:An.push(e)}function Yp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,ju(t)):(n.next=o.next,o.next=n),t.interleaved=n,Gt(e,r)}function Gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var on=!1;function Au(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Wt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function mn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Gt(e,n)}return o=r.interleaved,o===null?(t.next=t,ju(r)):(t.next=o.next,o.next=t),r.interleaved=t,Gt(e,n)}function ks(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,vu(e,n)}}function wd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ti(e,t,n,r){var o=e.updateQueue;on=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,i===null?s=u:i.next=u,i=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==i&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(s!==null){var m=o.baseState;i=0,f=u=a=null,l=s;do{var y=l.lane,w=l.eventTime;if((r&y)===y){f!==null&&(f=f.next={eventTime:w,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var S=e,p=l;switch(y=t,w=n,p.tag){case 1:if(S=p.payload,typeof S=="function"){m=S.call(w,m,y);break e}m=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=p.payload,y=typeof S=="function"?S.call(w,m,y):S,y==null)break e;m=de({},m,y);break e;case 2:on=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,y=o.effects,y===null?o.effects=[l]:y.push(l))}else w={eventTime:w,lane:y,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=w,a=m):f=f.next=w,i|=y;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;y=l,l=y.next,y.next=null,o.lastBaseUpdate=y,o.shared.pending=null}}while(!0);if(f===null&&(a=m),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);Un|=i,e.lanes=i,e.memoizedState=m}}function Sd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(b(191,o));o.call(r)}}}var Bo={},Ot=Cn(Bo),bo=Cn(Bo),_o=Cn(Bo);function On(e){if(e===Bo)throw Error(b(174));return e}function Ou(e,t){switch(ne(_o,t),ne(bo,e),ne(Ot,Bo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Zl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Zl(t,e)}le(Ot),ne(Ot,t)}function Pr(){le(Ot),le(bo),le(_o)}function Zp(e){On(_o.current);var t=On(Ot.current),n=Zl(t,e.type);t!==n&&(ne(bo,e),ne(Ot,n))}function Lu(e){bo.current===e&&(le(Ot),le(bo))}var ue=Cn(0);function ni(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var xl=[];function Du(){for(var e=0;e<xl.length;e++)xl[e]._workInProgressVersionPrimary=null;xl.length=0}var Ts=Jt.ReactCurrentDispatcher,wl=Jt.ReactCurrentBatchConfig,$n=0,ce=null,ye=null,Se=null,ri=!1,po=!1,jo=0,ew=0;function be(){throw Error(b(321))}function Mu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yt(e[n],t[n]))return!1;return!0}function Iu(e,t,n,r,o,s){if($n=s,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ts.current=e===null||e.memoizedState===null?ow:sw,e=n(r,o),po){s=0;do{if(po=!1,jo=0,25<=s)throw Error(b(301));s+=1,Se=ye=null,t.updateQueue=null,Ts.current=iw,e=n(r,o)}while(po)}if(Ts.current=oi,t=ye!==null&&ye.next!==null,$n=0,Se=ye=ce=null,ri=!1,t)throw Error(b(300));return e}function Fu(){var e=jo!==0;return jo=0,e}function Pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Se===null?ce.memoizedState=Se=e:Se=Se.next=e,Se}function ct(){if(ye===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=ye.next;var t=Se===null?ce.memoizedState:Se.next;if(t!==null)Se=t,ye=e;else{if(e===null)throw Error(b(310));ye=e,e={memoizedState:ye.memoizedState,baseState:ye.baseState,baseQueue:ye.baseQueue,queue:ye.queue,next:null},Se===null?ce.memoizedState=Se=e:Se=Se.next=e}return Se}function Ao(e,t){return typeof t=="function"?t(e):t}function Sl(e){var t=ct(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=ye,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var l=i=null,a=null,u=s;do{var f=u.lane;if(($n&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var m={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=m,i=r):a=a.next=m,ce.lanes|=f,Un|=f}u=u.next}while(u!==null&&u!==s);a===null?i=r:a.next=l,yt(r,t.memoizedState)||(Be=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ce.lanes|=s,Un|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function El(e){var t=ct(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);yt(s,t.memoizedState)||(Be=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function em(){}function tm(e,t){var n=ce,r=ct(),o=t(),s=!yt(r.memoizedState,o);if(s&&(r.memoizedState=o,Be=!0),r=r.queue,zu(om.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||Se!==null&&Se.memoizedState.tag&1){if(n.flags|=2048,Oo(9,rm.bind(null,n,r,o,t),void 0,null),Ce===null)throw Error(b(349));$n&30||nm(n,t,o)}return o}function nm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function rm(e,t,n,r){t.value=n,t.getSnapshot=r,sm(t)&&im(e)}function om(e,t,n){return n(function(){sm(t)&&im(e)})}function sm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yt(e,n)}catch{return!0}}function im(e){var t=Gt(e,1);t!==null&&vt(t,e,1,-1)}function Ed(e){var t=Pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ao,lastRenderedState:e},t.queue=e,e=e.dispatch=rw.bind(null,ce,e),[t.memoizedState,e]}function Oo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function lm(){return ct().memoizedState}function Rs(e,t,n,r){var o=Pt();ce.flags|=e,o.memoizedState=Oo(1|t,n,void 0,r===void 0?null:r)}function Ti(e,t,n,r){var o=ct();r=r===void 0?null:r;var s=void 0;if(ye!==null){var i=ye.memoizedState;if(s=i.destroy,r!==null&&Mu(r,i.deps)){o.memoizedState=Oo(t,n,s,r);return}}ce.flags|=e,o.memoizedState=Oo(1|t,n,s,r)}function Cd(e,t){return Rs(8390656,8,e,t)}function zu(e,t){return Ti(2048,8,e,t)}function am(e,t){return Ti(4,2,e,t)}function um(e,t){return Ti(4,4,e,t)}function cm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function dm(e,t,n){return n=n!=null?n.concat([e]):null,Ti(4,4,cm.bind(null,t,e),n)}function $u(){}function fm(e,t){var n=ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function pm(e,t){var n=ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function mm(e,t,n){return $n&21?(yt(n,t)||(n=xp(),ce.lanes|=n,Un|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Be=!0),e.memoizedState=n)}function tw(e,t){var n=Y;Y=n!==0&&4>n?n:4,e(!0);var r=wl.transition;wl.transition={};try{e(!1),t()}finally{Y=n,wl.transition=r}}function hm(){return ct().memoizedState}function nw(e,t,n){var r=gn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},gm(e))vm(t,n);else if(n=Yp(e,t,n,r),n!==null){var o=Fe();vt(n,e,r,o),ym(n,t,r)}}function rw(e,t,n){var r=gn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(gm(e))vm(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,l=s(i,n);if(o.hasEagerState=!0,o.eagerState=l,yt(l,i)){var a=t.interleaved;a===null?(o.next=o,ju(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Yp(e,t,o,r),n!==null&&(o=Fe(),vt(n,e,r,o),ym(n,t,r))}}function gm(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function vm(e,t){po=ri=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ym(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,vu(e,n)}}var oi={readContext:ut,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useInsertionEffect:be,useLayoutEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useMutableSource:be,useSyncExternalStore:be,useId:be,unstable_isNewReconciler:!1},ow={readContext:ut,useCallback:function(e,t){return Pt().memoizedState=[e,t===void 0?null:t],e},useContext:ut,useEffect:Cd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Rs(4194308,4,cm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Rs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Rs(4,2,e,t)},useMemo:function(e,t){var n=Pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=nw.bind(null,ce,e),[r.memoizedState,e]},useRef:function(e){var t=Pt();return e={current:e},t.memoizedState=e},useState:Ed,useDebugValue:$u,useDeferredValue:function(e){return Pt().memoizedState=e},useTransition:function(){var e=Ed(!1),t=e[0];return e=tw.bind(null,e[1]),Pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ce,o=Pt();if(ae){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),Ce===null)throw Error(b(349));$n&30||nm(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,Cd(om.bind(null,r,s,e),[e]),r.flags|=2048,Oo(9,rm.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Pt(),t=Ce.identifierPrefix;if(ae){var n=Bt,r=Ut;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=jo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ew++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},sw={readContext:ut,useCallback:fm,useContext:ut,useEffect:zu,useImperativeHandle:dm,useInsertionEffect:am,useLayoutEffect:um,useMemo:pm,useReducer:Sl,useRef:lm,useState:function(){return Sl(Ao)},useDebugValue:$u,useDeferredValue:function(e){var t=ct();return mm(t,ye.memoizedState,e)},useTransition:function(){var e=Sl(Ao)[0],t=ct().memoizedState;return[e,t]},useMutableSource:em,useSyncExternalStore:tm,useId:hm,unstable_isNewReconciler:!1},iw={readContext:ut,useCallback:fm,useContext:ut,useEffect:zu,useImperativeHandle:dm,useInsertionEffect:am,useLayoutEffect:um,useMemo:pm,useReducer:El,useRef:lm,useState:function(){return El(Ao)},useDebugValue:$u,useDeferredValue:function(e){var t=ct();return ye===null?t.memoizedState=e:mm(t,ye.memoizedState,e)},useTransition:function(){var e=El(Ao)[0],t=ct().memoizedState;return[e,t]},useMutableSource:em,useSyncExternalStore:tm,useId:hm,unstable_isNewReconciler:!1};function pt(e,t){if(e&&e.defaultProps){t=de({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function wa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:de({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ri={isMounted:function(e){return(e=e._reactInternals)?Xn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Fe(),o=gn(e),s=Wt(r,o);s.payload=t,n!=null&&(s.callback=n),t=mn(e,s,o),t!==null&&(vt(t,e,o,r),ks(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Fe(),o=gn(e),s=Wt(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=mn(e,s,o),t!==null&&(vt(t,e,o,r),ks(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Fe(),r=gn(e),o=Wt(n,r);o.tag=2,t!=null&&(o.callback=t),t=mn(e,o,r),t!==null&&(vt(t,e,r,n),ks(t,e,r))}};function Nd(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!ko(n,r)||!ko(o,s):!0}function xm(e,t,n){var r=!1,o=xn,s=t.contextType;return typeof s=="object"&&s!==null?s=ut(s):(o=We(t)?Fn:Oe.current,r=t.contextTypes,s=(r=r!=null)?kr(e,o):xn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ri,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function kd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ri.enqueueReplaceState(t,t.state,null)}function Sa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Au(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=ut(s):(s=We(t)?Fn:Oe.current,o.context=kr(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(wa(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ri.enqueueReplaceState(o,o.state,null),ti(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function br(e,t){try{var n="",r=t;do n+=Ly(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Cl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ea(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var lw=typeof WeakMap=="function"?WeakMap:Map;function wm(e,t,n){n=Wt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ii||(ii=!0,Aa=r),Ea(e,t)},n}function Sm(e,t,n){n=Wt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ea(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ea(e,t),typeof r!="function"&&(hn===null?hn=new Set([this]):hn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Td(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new lw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Sw.bind(null,e,t,n),t.then(e,e))}function Rd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Pd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Wt(-1,1),t.tag=2,mn(n,t,1))),n.lanes|=1),e)}var aw=Jt.ReactCurrentOwner,Be=!1;function Me(e,t,n,r){t.child=e===null?qp(t,null,n,r):Rr(t,e.child,n,r)}function bd(e,t,n,r,o){n=n.render;var s=t.ref;return yr(t,o),r=Iu(e,t,n,r,s,o),n=Fu(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Xt(e,t,o)):(ae&&n&&ku(t),t.flags|=1,Me(e,t,r,o),t.child)}function _d(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Gu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Em(e,t,s,r,o)):(e=js(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:ko,n(i,r)&&e.ref===t.ref)return Xt(e,t,o)}return t.flags|=1,e=vn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Em(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(ko(s,r)&&e.ref===t.ref)if(Be=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Be=!0);else return t.lanes=e.lanes,Xt(e,t,o)}return Ca(e,t,n,r,o)}function Cm(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ne(pr,Xe),Xe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ne(pr,Xe),Xe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ne(pr,Xe),Xe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ne(pr,Xe),Xe|=r;return Me(e,t,o,n),t.child}function Nm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ca(e,t,n,r,o){var s=We(n)?Fn:Oe.current;return s=kr(t,s),yr(t,o),n=Iu(e,t,n,r,s,o),r=Fu(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Xt(e,t,o)):(ae&&r&&ku(t),t.flags|=1,Me(e,t,n,o),t.child)}function jd(e,t,n,r,o){if(We(n)){var s=!0;qs(t)}else s=!1;if(yr(t,o),t.stateNode===null)Ps(e,t),xm(t,n,r),Sa(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,l=t.memoizedProps;i.props=l;var a=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=ut(u):(u=We(n)?Fn:Oe.current,u=kr(t,u));var f=n.getDerivedStateFromProps,m=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";m||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==r||a!==u)&&kd(t,i,r,u),on=!1;var y=t.memoizedState;i.state=y,ti(t,r,i,o),a=t.memoizedState,l!==r||y!==a||Ve.current||on?(typeof f=="function"&&(wa(t,n,f,r),a=t.memoizedState),(l=on||Nd(t,n,l,r,y,a,u))?(m||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),i.props=r,i.state=a,i.context=u,r=l):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Jp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:pt(t.type,l),i.props=u,m=t.pendingProps,y=i.context,a=n.contextType,typeof a=="object"&&a!==null?a=ut(a):(a=We(n)?Fn:Oe.current,a=kr(t,a));var w=n.getDerivedStateFromProps;(f=typeof w=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==m||y!==a)&&kd(t,i,r,a),on=!1,y=t.memoizedState,i.state=y,ti(t,r,i,o);var S=t.memoizedState;l!==m||y!==S||Ve.current||on?(typeof w=="function"&&(wa(t,n,w,r),S=t.memoizedState),(u=on||Nd(t,n,u,r,y,S,a)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,S,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,S,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=S),i.props=r,i.state=S,i.context=a,r=u):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return Na(e,t,n,r,s,o)}function Na(e,t,n,r,o,s){Nm(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&gd(t,n,!1),Xt(e,t,s);r=t.stateNode,aw.current=t;var l=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Rr(t,e.child,null,s),t.child=Rr(t,null,l,s)):Me(e,t,l,s),t.memoizedState=r.state,o&&gd(t,n,!0),t.child}function km(e){var t=e.stateNode;t.pendingContext?hd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&hd(e,t.context,!1),Ou(e,t.containerInfo)}function Ad(e,t,n,r,o){return Tr(),Ru(o),t.flags|=256,Me(e,t,n,r),t.child}var ka={dehydrated:null,treeContext:null,retryLane:0};function Ta(e){return{baseLanes:e,cachePool:null,transitions:null}}function Tm(e,t,n){var r=t.pendingProps,o=ue.current,s=!1,i=(t.flags&128)!==0,l;if((l=i)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ne(ue,o&1),e===null)return ya(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=_i(i,r,0,null),e=Mn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ta(n),t.memoizedState=ka,e):Uu(t,i));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return uw(e,t,i,r,l,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=vn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?s=vn(l,s):(s=Mn(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?Ta(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=ka,r}return s=e.child,e=s.sibling,r=vn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Uu(e,t){return t=_i({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function us(e,t,n,r){return r!==null&&Ru(r),Rr(t,e.child,null,n),e=Uu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function uw(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=Cl(Error(b(422))),us(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=_i({mode:"visible",children:r.children},o,0,null),s=Mn(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Rr(t,e.child,null,i),t.child.memoizedState=Ta(i),t.memoizedState=ka,s);if(!(t.mode&1))return us(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(b(419)),r=Cl(s,r,void 0),us(e,t,i,r)}if(l=(i&e.childLanes)!==0,Be||l){if(r=Ce,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,Gt(e,o),vt(r,e,o,-1))}return Qu(),r=Cl(Error(b(421))),us(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Ew.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,Ze=pn(o.nextSibling),et=t,ae=!0,ht=null,e!==null&&(st[it++]=Ut,st[it++]=Bt,st[it++]=zn,Ut=e.id,Bt=e.overflow,zn=t),t=Uu(t,r.children),t.flags|=4096,t)}function Od(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),xa(e.return,t,n)}function Nl(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Rm(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Me(e,t,r.children,n),r=ue.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Od(e,n,t);else if(e.tag===19)Od(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ne(ue,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ni(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Nl(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ni(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Nl(t,!0,n,null,s);break;case"together":Nl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ps(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Xt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Un|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=vn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=vn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function cw(e,t,n){switch(t.tag){case 3:km(t),Tr();break;case 5:Zp(t);break;case 1:We(t.type)&&qs(t);break;case 4:Ou(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ne(Zs,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ne(ue,ue.current&1),t.flags|=128,null):n&t.child.childLanes?Tm(e,t,n):(ne(ue,ue.current&1),e=Xt(e,t,n),e!==null?e.sibling:null);ne(ue,ue.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Rm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ne(ue,ue.current),r)break;return null;case 22:case 23:return t.lanes=0,Cm(e,t,n)}return Xt(e,t,n)}var Pm,Ra,bm,_m;Pm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ra=function(){};bm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,On(Ot.current);var s=null;switch(n){case"input":o=Xl(e,o),r=Xl(e,r),s=[];break;case"select":o=de({},o,{value:void 0}),r=de({},r,{value:void 0}),s=[];break;case"textarea":o=Jl(e,o),r=Jl(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Gs)}ea(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(yo.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(i in l)!l.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in a)a.hasOwnProperty(i)&&l[i]!==a[i]&&(n||(n={}),n[i]=a[i])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(yo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&ie("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};_m=function(e,t,n,r){n!==r&&(t.flags|=4)};function Yr(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function _e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function dw(e,t,n){var r=t.pendingProps;switch(Tu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _e(t),null;case 1:return We(t.type)&&Xs(),_e(t),null;case 3:return r=t.stateNode,Pr(),le(Ve),le(Oe),Du(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ls(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ht!==null&&(Da(ht),ht=null))),Ra(e,t),_e(t),null;case 5:Lu(t);var o=On(_o.current);if(n=t.type,e!==null&&t.stateNode!=null)bm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return _e(t),null}if(e=On(Ot.current),ls(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[bt]=t,r[Po]=s,e=(t.mode&1)!==0,n){case"dialog":ie("cancel",r),ie("close",r);break;case"iframe":case"object":case"embed":ie("load",r);break;case"video":case"audio":for(o=0;o<so.length;o++)ie(so[o],r);break;case"source":ie("error",r);break;case"img":case"image":case"link":ie("error",r),ie("load",r);break;case"details":ie("toggle",r);break;case"input":Bc(r,s),ie("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ie("invalid",r);break;case"textarea":Wc(r,s),ie("invalid",r)}ea(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&is(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&is(r.textContent,l,e),o=["children",""+l]):yo.hasOwnProperty(i)&&l!=null&&i==="onScroll"&&ie("scroll",r)}switch(n){case"input":Jo(r),Vc(r,s,!0);break;case"textarea":Jo(r),Hc(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Gs)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=op(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[bt]=t,e[Po]=r,Pm(e,t,!1,!1),t.stateNode=e;e:{switch(i=ta(n,r),n){case"dialog":ie("cancel",e),ie("close",e),o=r;break;case"iframe":case"object":case"embed":ie("load",e),o=r;break;case"video":case"audio":for(o=0;o<so.length;o++)ie(so[o],e);o=r;break;case"source":ie("error",e),o=r;break;case"img":case"image":case"link":ie("error",e),ie("load",e),o=r;break;case"details":ie("toggle",e),o=r;break;case"input":Bc(e,r),o=Xl(e,r),ie("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=de({},r,{value:void 0}),ie("invalid",e);break;case"textarea":Wc(e,r),o=Jl(e,r),ie("invalid",e);break;default:o=r}ea(n,o),l=o;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?lp(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&sp(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&xo(e,a):typeof a=="number"&&xo(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(yo.hasOwnProperty(s)?a!=null&&s==="onScroll"&&ie("scroll",e):a!=null&&du(e,s,a,i))}switch(n){case"input":Jo(e),Vc(e,r,!1);break;case"textarea":Jo(e),Hc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+yn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?mr(e,!!r.multiple,s,!1):r.defaultValue!=null&&mr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Gs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return _e(t),null;case 6:if(e&&t.stateNode!=null)_m(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=On(_o.current),On(Ot.current),ls(t)){if(r=t.stateNode,n=t.memoizedProps,r[bt]=t,(s=r.nodeValue!==n)&&(e=et,e!==null))switch(e.tag){case 3:is(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&is(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[bt]=t,t.stateNode=r}return _e(t),null;case 13:if(le(ue),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&Ze!==null&&t.mode&1&&!(t.flags&128))Gp(),Tr(),t.flags|=98560,s=!1;else if(s=ls(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(b(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(b(317));s[bt]=t}else Tr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;_e(t),s=!1}else ht!==null&&(Da(ht),ht=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ue.current&1?xe===0&&(xe=3):Qu())),t.updateQueue!==null&&(t.flags|=4),_e(t),null);case 4:return Pr(),Ra(e,t),e===null&&To(t.stateNode.containerInfo),_e(t),null;case 10:return _u(t.type._context),_e(t),null;case 17:return We(t.type)&&Xs(),_e(t),null;case 19:if(le(ue),s=t.memoizedState,s===null)return _e(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Yr(s,!1);else{if(xe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ni(e),i!==null){for(t.flags|=128,Yr(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ne(ue,ue.current&1|2),t.child}e=e.sibling}s.tail!==null&&me()>_r&&(t.flags|=128,r=!0,Yr(s,!1),t.lanes=4194304)}else{if(!r)if(e=ni(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Yr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!ae)return _e(t),null}else 2*me()-s.renderingStartTime>_r&&n!==1073741824&&(t.flags|=128,r=!0,Yr(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=me(),t.sibling=null,n=ue.current,ne(ue,r?n&1|2:n&1),t):(_e(t),null);case 22:case 23:return Ku(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Xe&1073741824&&(_e(t),t.subtreeFlags&6&&(t.flags|=8192)):_e(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function fw(e,t){switch(Tu(t),t.tag){case 1:return We(t.type)&&Xs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Pr(),le(Ve),le(Oe),Du(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Lu(t),null;case 13:if(le(ue),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));Tr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(ue),null;case 4:return Pr(),null;case 10:return _u(t.type._context),null;case 22:case 23:return Ku(),null;case 24:return null;default:return null}}var cs=!1,je=!1,pw=typeof WeakSet=="function"?WeakSet:Set,D=null;function fr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){pe(e,t,r)}else n.current=null}function Pa(e,t,n){try{n()}catch(r){pe(e,t,r)}}var Ld=!1;function mw(e,t){if(da=Hs,e=Dp(),Nu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,l=-1,a=-1,u=0,f=0,m=e,y=null;t:for(;;){for(var w;m!==n||o!==0&&m.nodeType!==3||(l=i+o),m!==s||r!==0&&m.nodeType!==3||(a=i+r),m.nodeType===3&&(i+=m.nodeValue.length),(w=m.firstChild)!==null;)y=m,m=w;for(;;){if(m===e)break t;if(y===n&&++u===o&&(l=i),y===s&&++f===r&&(a=i),(w=m.nextSibling)!==null)break;m=y,y=m.parentNode}m=w}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(fa={focusedElem:e,selectionRange:n},Hs=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var p=S.memoizedProps,x=S.memoizedState,h=t.stateNode,d=h.getSnapshotBeforeUpdate(t.elementType===t.type?p:pt(t.type,p),x);h.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(E){pe(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return S=Ld,Ld=!1,S}function mo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Pa(t,n,s)}o=o.next}while(o!==r)}}function Pi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ba(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function jm(e){var t=e.alternate;t!==null&&(e.alternate=null,jm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[bt],delete t[Po],delete t[ha],delete t[qx],delete t[Yx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Am(e){return e.tag===5||e.tag===3||e.tag===4}function Dd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Am(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function _a(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Gs));else if(r!==4&&(e=e.child,e!==null))for(_a(e,t,n),e=e.sibling;e!==null;)_a(e,t,n),e=e.sibling}function ja(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ja(e,t,n),e=e.sibling;e!==null;)ja(e,t,n),e=e.sibling}var Ne=null,mt=!1;function Zt(e,t,n){for(n=n.child;n!==null;)Om(e,t,n),n=n.sibling}function Om(e,t,n){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(wi,n)}catch{}switch(n.tag){case 5:je||fr(n,t);case 6:var r=Ne,o=mt;Ne=null,Zt(e,t,n),Ne=r,mt=o,Ne!==null&&(mt?(e=Ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(mt?(e=Ne,n=n.stateNode,e.nodeType===8?vl(e.parentNode,n):e.nodeType===1&&vl(e,n),Co(e)):vl(Ne,n.stateNode));break;case 4:r=Ne,o=mt,Ne=n.stateNode.containerInfo,mt=!0,Zt(e,t,n),Ne=r,mt=o;break;case 0:case 11:case 14:case 15:if(!je&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&Pa(n,t,i),o=o.next}while(o!==r)}Zt(e,t,n);break;case 1:if(!je&&(fr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){pe(n,t,l)}Zt(e,t,n);break;case 21:Zt(e,t,n);break;case 22:n.mode&1?(je=(r=je)||n.memoizedState!==null,Zt(e,t,n),je=r):Zt(e,t,n);break;default:Zt(e,t,n)}}function Md(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new pw),t.forEach(function(r){var o=Cw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function dt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,l=i;e:for(;l!==null;){switch(l.tag){case 5:Ne=l.stateNode,mt=!1;break e;case 3:Ne=l.stateNode.containerInfo,mt=!0;break e;case 4:Ne=l.stateNode.containerInfo,mt=!0;break e}l=l.return}if(Ne===null)throw Error(b(160));Om(s,i,o),Ne=null,mt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){pe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Lm(t,e),t=t.sibling}function Lm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(dt(t,e),Tt(e),r&4){try{mo(3,e,e.return),Pi(3,e)}catch(p){pe(e,e.return,p)}try{mo(5,e,e.return)}catch(p){pe(e,e.return,p)}}break;case 1:dt(t,e),Tt(e),r&512&&n!==null&&fr(n,n.return);break;case 5:if(dt(t,e),Tt(e),r&512&&n!==null&&fr(n,n.return),e.flags&32){var o=e.stateNode;try{xo(o,"")}catch(p){pe(e,e.return,p)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&np(o,s),ta(l,i);var u=ta(l,s);for(i=0;i<a.length;i+=2){var f=a[i],m=a[i+1];f==="style"?lp(o,m):f==="dangerouslySetInnerHTML"?sp(o,m):f==="children"?xo(o,m):du(o,f,m,u)}switch(l){case"input":ql(o,s);break;case"textarea":rp(o,s);break;case"select":var y=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var w=s.value;w!=null?mr(o,!!s.multiple,w,!1):y!==!!s.multiple&&(s.defaultValue!=null?mr(o,!!s.multiple,s.defaultValue,!0):mr(o,!!s.multiple,s.multiple?[]:"",!1))}o[Po]=s}catch(p){pe(e,e.return,p)}}break;case 6:if(dt(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(b(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(p){pe(e,e.return,p)}}break;case 3:if(dt(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Co(t.containerInfo)}catch(p){pe(e,e.return,p)}break;case 4:dt(t,e),Tt(e);break;case 13:dt(t,e),Tt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Wu=me())),r&4&&Md(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(je=(u=je)||f,dt(t,e),je=u):dt(t,e),Tt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(D=e,f=e.child;f!==null;){for(m=D=f;D!==null;){switch(y=D,w=y.child,y.tag){case 0:case 11:case 14:case 15:mo(4,y,y.return);break;case 1:fr(y,y.return);var S=y.stateNode;if(typeof S.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(p){pe(r,n,p)}}break;case 5:fr(y,y.return);break;case 22:if(y.memoizedState!==null){Fd(m);continue}}w!==null?(w.return=y,D=w):Fd(m)}f=f.sibling}e:for(f=null,m=e;;){if(m.tag===5){if(f===null){f=m;try{o=m.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=m.stateNode,a=m.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=ip("display",i))}catch(p){pe(e,e.return,p)}}}else if(m.tag===6){if(f===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(p){pe(e,e.return,p)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;f===m&&(f=null),m=m.return}f===m&&(f=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:dt(t,e),Tt(e),r&4&&Md(e);break;case 21:break;default:dt(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Am(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(xo(o,""),r.flags&=-33);var s=Dd(e);ja(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,l=Dd(e);_a(e,l,i);break;default:throw Error(b(161))}}catch(a){pe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function hw(e,t,n){D=e,Dm(e)}function Dm(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var o=D,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||cs;if(!i){var l=o.alternate,a=l!==null&&l.memoizedState!==null||je;l=cs;var u=je;if(cs=i,(je=a)&&!u)for(D=o;D!==null;)i=D,a=i.child,i.tag===22&&i.memoizedState!==null?zd(o):a!==null?(a.return=i,D=a):zd(o);for(;s!==null;)D=s,Dm(s),s=s.sibling;D=o,cs=l,je=u}Id(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,D=s):Id(e)}}function Id(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:je||Pi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!je)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:pt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Sd(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Sd(t,i,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var m=f.dehydrated;m!==null&&Co(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}je||t.flags&512&&ba(t)}catch(y){pe(t,t.return,y)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function Fd(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function zd(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Pi(4,t)}catch(a){pe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){pe(t,o,a)}}var s=t.return;try{ba(t)}catch(a){pe(t,s,a)}break;case 5:var i=t.return;try{ba(t)}catch(a){pe(t,i,a)}}}catch(a){pe(t,t.return,a)}if(t===e){D=null;break}var l=t.sibling;if(l!==null){l.return=t.return,D=l;break}D=t.return}}var gw=Math.ceil,si=Jt.ReactCurrentDispatcher,Bu=Jt.ReactCurrentOwner,at=Jt.ReactCurrentBatchConfig,X=0,Ce=null,ge=null,ke=0,Xe=0,pr=Cn(0),xe=0,Lo=null,Un=0,bi=0,Vu=0,ho=null,Ue=null,Wu=0,_r=1/0,zt=null,ii=!1,Aa=null,hn=null,ds=!1,un=null,li=0,go=0,Oa=null,bs=-1,_s=0;function Fe(){return X&6?me():bs!==-1?bs:bs=me()}function gn(e){return e.mode&1?X&2&&ke!==0?ke&-ke:Zx.transition!==null?(_s===0&&(_s=xp()),_s):(e=Y,e!==0||(e=window.event,e=e===void 0?16:Tp(e.type)),e):1}function vt(e,t,n,r){if(50<go)throw go=0,Oa=null,Error(b(185));zo(e,n,r),(!(X&2)||e!==Ce)&&(e===Ce&&(!(X&2)&&(bi|=n),xe===4&&ln(e,ke)),He(e,r),n===1&&X===0&&!(t.mode&1)&&(_r=me()+500,ki&&Nn()))}function He(e,t){var n=e.callbackNode;Zy(e,t);var r=Ws(e,e===Ce?ke:0);if(r===0)n!==null&&Gc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Gc(n),t===1)e.tag===0?Jx($d.bind(null,e)):Hp($d.bind(null,e)),Gx(function(){!(X&6)&&Nn()}),n=null;else{switch(wp(r)){case 1:n=gu;break;case 4:n=vp;break;case 16:n=Vs;break;case 536870912:n=yp;break;default:n=Vs}n=Vm(n,Mm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Mm(e,t){if(bs=-1,_s=0,X&6)throw Error(b(327));var n=e.callbackNode;if(xr()&&e.callbackNode!==n)return null;var r=Ws(e,e===Ce?ke:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ai(e,r);else{t=r;var o=X;X|=2;var s=Fm();(Ce!==e||ke!==t)&&(zt=null,_r=me()+500,Dn(e,t));do try{xw();break}catch(l){Im(e,l)}while(!0);bu(),si.current=s,X=o,ge!==null?t=0:(Ce=null,ke=0,t=xe)}if(t!==0){if(t===2&&(o=ia(e),o!==0&&(r=o,t=La(e,o))),t===1)throw n=Lo,Dn(e,0),ln(e,r),He(e,me()),n;if(t===6)ln(e,r);else{if(o=e.current.alternate,!(r&30)&&!vw(o)&&(t=ai(e,r),t===2&&(s=ia(e),s!==0&&(r=s,t=La(e,s))),t===1))throw n=Lo,Dn(e,0),ln(e,r),He(e,me()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:Pn(e,Ue,zt);break;case 3:if(ln(e,r),(r&130023424)===r&&(t=Wu+500-me(),10<t)){if(Ws(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Fe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ma(Pn.bind(null,e,Ue,zt),t);break}Pn(e,Ue,zt);break;case 4:if(ln(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-gt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=me()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*gw(r/1960))-r,10<r){e.timeoutHandle=ma(Pn.bind(null,e,Ue,zt),r);break}Pn(e,Ue,zt);break;case 5:Pn(e,Ue,zt);break;default:throw Error(b(329))}}}return He(e,me()),e.callbackNode===n?Mm.bind(null,e):null}function La(e,t){var n=ho;return e.current.memoizedState.isDehydrated&&(Dn(e,t).flags|=256),e=ai(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&Da(t)),e}function Da(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function vw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!yt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ln(e,t){for(t&=~Vu,t&=~bi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-gt(t),r=1<<n;e[n]=-1,t&=~r}}function $d(e){if(X&6)throw Error(b(327));xr();var t=Ws(e,0);if(!(t&1))return He(e,me()),null;var n=ai(e,t);if(e.tag!==0&&n===2){var r=ia(e);r!==0&&(t=r,n=La(e,r))}if(n===1)throw n=Lo,Dn(e,0),ln(e,t),He(e,me()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Pn(e,Ue,zt),He(e,me()),null}function Hu(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(_r=me()+500,ki&&Nn())}}function Bn(e){un!==null&&un.tag===0&&!(X&6)&&xr();var t=X;X|=1;var n=at.transition,r=Y;try{if(at.transition=null,Y=1,e)return e()}finally{Y=r,at.transition=n,X=t,!(X&6)&&Nn()}}function Ku(){Xe=pr.current,le(pr)}function Dn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Qx(n)),ge!==null)for(n=ge.return;n!==null;){var r=n;switch(Tu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Xs();break;case 3:Pr(),le(Ve),le(Oe),Du();break;case 5:Lu(r);break;case 4:Pr();break;case 13:le(ue);break;case 19:le(ue);break;case 10:_u(r.type._context);break;case 22:case 23:Ku()}n=n.return}if(Ce=e,ge=e=vn(e.current,null),ke=Xe=t,xe=0,Lo=null,Vu=bi=Un=0,Ue=ho=null,An!==null){for(t=0;t<An.length;t++)if(n=An[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}An=null}return e}function Im(e,t){do{var n=ge;try{if(bu(),Ts.current=oi,ri){for(var r=ce.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ri=!1}if($n=0,Se=ye=ce=null,po=!1,jo=0,Bu.current=null,n===null||n.return===null){xe=1,Lo=t,ge=null;break}e:{var s=e,i=n.return,l=n,a=t;if(t=ke,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,m=f.tag;if(!(f.mode&1)&&(m===0||m===11||m===15)){var y=f.alternate;y?(f.updateQueue=y.updateQueue,f.memoizedState=y.memoizedState,f.lanes=y.lanes):(f.updateQueue=null,f.memoizedState=null)}var w=Rd(i);if(w!==null){w.flags&=-257,Pd(w,i,l,s,t),w.mode&1&&Td(s,u,t),t=w,a=u;var S=t.updateQueue;if(S===null){var p=new Set;p.add(a),t.updateQueue=p}else S.add(a);break e}else{if(!(t&1)){Td(s,u,t),Qu();break e}a=Error(b(426))}}else if(ae&&l.mode&1){var x=Rd(i);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Pd(x,i,l,s,t),Ru(br(a,l));break e}}s=a=br(a,l),xe!==4&&(xe=2),ho===null?ho=[s]:ho.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var h=wm(s,a,t);wd(s,h);break e;case 1:l=a;var d=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof d.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(hn===null||!hn.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var E=Sm(s,l,t);wd(s,E);break e}}s=s.return}while(s!==null)}$m(n)}catch(C){t=C,ge===n&&n!==null&&(ge=n=n.return);continue}break}while(!0)}function Fm(){var e=si.current;return si.current=oi,e===null?oi:e}function Qu(){(xe===0||xe===3||xe===2)&&(xe=4),Ce===null||!(Un&268435455)&&!(bi&268435455)||ln(Ce,ke)}function ai(e,t){var n=X;X|=2;var r=Fm();(Ce!==e||ke!==t)&&(zt=null,Dn(e,t));do try{yw();break}catch(o){Im(e,o)}while(!0);if(bu(),X=n,si.current=r,ge!==null)throw Error(b(261));return Ce=null,ke=0,xe}function yw(){for(;ge!==null;)zm(ge)}function xw(){for(;ge!==null&&!Wy();)zm(ge)}function zm(e){var t=Bm(e.alternate,e,Xe);e.memoizedProps=e.pendingProps,t===null?$m(e):ge=t,Bu.current=null}function $m(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=fw(n,t),n!==null){n.flags&=32767,ge=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{xe=6,ge=null;return}}else if(n=dw(n,t,Xe),n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);xe===0&&(xe=5)}function Pn(e,t,n){var r=Y,o=at.transition;try{at.transition=null,Y=1,ww(e,t,n,r)}finally{at.transition=o,Y=r}return null}function ww(e,t,n,r){do xr();while(un!==null);if(X&6)throw Error(b(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(ex(e,s),e===Ce&&(ge=Ce=null,ke=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ds||(ds=!0,Vm(Vs,function(){return xr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=at.transition,at.transition=null;var i=Y;Y=1;var l=X;X|=4,Bu.current=null,mw(e,n),Lm(n,e),$x(fa),Hs=!!da,fa=da=null,e.current=n,hw(n),Hy(),X=l,Y=i,at.transition=s}else e.current=n;if(ds&&(ds=!1,un=e,li=o),s=e.pendingLanes,s===0&&(hn=null),Gy(n.stateNode),He(e,me()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ii)throw ii=!1,e=Aa,Aa=null,e;return li&1&&e.tag!==0&&xr(),s=e.pendingLanes,s&1?e===Oa?go++:(go=0,Oa=e):go=0,Nn(),null}function xr(){if(un!==null){var e=wp(li),t=at.transition,n=Y;try{if(at.transition=null,Y=16>e?16:e,un===null)var r=!1;else{if(e=un,un=null,li=0,X&6)throw Error(b(331));var o=X;for(X|=4,D=e.current;D!==null;){var s=D,i=s.child;if(D.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(D=u;D!==null;){var f=D;switch(f.tag){case 0:case 11:case 15:mo(8,f,s)}var m=f.child;if(m!==null)m.return=f,D=m;else for(;D!==null;){f=D;var y=f.sibling,w=f.return;if(jm(f),f===u){D=null;break}if(y!==null){y.return=w,D=y;break}D=w}}}var S=s.alternate;if(S!==null){var p=S.child;if(p!==null){S.child=null;do{var x=p.sibling;p.sibling=null,p=x}while(p!==null)}}D=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,D=i;else e:for(;D!==null;){if(s=D,s.flags&2048)switch(s.tag){case 0:case 11:case 15:mo(9,s,s.return)}var h=s.sibling;if(h!==null){h.return=s.return,D=h;break e}D=s.return}}var d=e.current;for(D=d;D!==null;){i=D;var v=i.child;if(i.subtreeFlags&2064&&v!==null)v.return=i,D=v;else e:for(i=d;D!==null;){if(l=D,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Pi(9,l)}}catch(C){pe(l,l.return,C)}if(l===i){D=null;break e}var E=l.sibling;if(E!==null){E.return=l.return,D=E;break e}D=l.return}}if(X=o,Nn(),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(wi,e)}catch{}r=!0}return r}finally{Y=n,at.transition=t}}return!1}function Ud(e,t,n){t=br(n,t),t=wm(e,t,1),e=mn(e,t,1),t=Fe(),e!==null&&(zo(e,1,t),He(e,t))}function pe(e,t,n){if(e.tag===3)Ud(e,e,n);else for(;t!==null;){if(t.tag===3){Ud(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(hn===null||!hn.has(r))){e=br(n,e),e=Sm(t,e,1),t=mn(t,e,1),e=Fe(),t!==null&&(zo(t,1,e),He(t,e));break}}t=t.return}}function Sw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Fe(),e.pingedLanes|=e.suspendedLanes&n,Ce===e&&(ke&n)===n&&(xe===4||xe===3&&(ke&130023424)===ke&&500>me()-Wu?Dn(e,0):Vu|=n),He(e,t)}function Um(e,t){t===0&&(e.mode&1?(t=ts,ts<<=1,!(ts&130023424)&&(ts=4194304)):t=1);var n=Fe();e=Gt(e,t),e!==null&&(zo(e,t,n),He(e,n))}function Ew(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Um(e,n)}function Cw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),Um(e,n)}var Bm;Bm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ve.current)Be=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Be=!1,cw(e,t,n);Be=!!(e.flags&131072)}else Be=!1,ae&&t.flags&1048576&&Kp(t,Js,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ps(e,t),e=t.pendingProps;var o=kr(t,Oe.current);yr(t,n),o=Iu(null,t,r,e,o,n);var s=Fu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,We(r)?(s=!0,qs(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Au(t),o.updater=Ri,t.stateNode=o,o._reactInternals=t,Sa(t,r,e,n),t=Na(null,t,r,!0,s,n)):(t.tag=0,ae&&s&&ku(t),Me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ps(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=kw(r),e=pt(r,e),o){case 0:t=Ca(null,t,r,e,n);break e;case 1:t=jd(null,t,r,e,n);break e;case 11:t=bd(null,t,r,e,n);break e;case 14:t=_d(null,t,r,pt(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),Ca(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),jd(e,t,r,o,n);case 3:e:{if(km(t),e===null)throw Error(b(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Jp(e,t),ti(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=br(Error(b(423)),t),t=Ad(e,t,r,n,o);break e}else if(r!==o){o=br(Error(b(424)),t),t=Ad(e,t,r,n,o);break e}else for(Ze=pn(t.stateNode.containerInfo.firstChild),et=t,ae=!0,ht=null,n=qp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Tr(),r===o){t=Xt(e,t,n);break e}Me(e,t,r,n)}t=t.child}return t;case 5:return Zp(t),e===null&&ya(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,pa(r,o)?i=null:s!==null&&pa(r,s)&&(t.flags|=32),Nm(e,t),Me(e,t,i,n),t.child;case 6:return e===null&&ya(t),null;case 13:return Tm(e,t,n);case 4:return Ou(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Rr(t,null,r,n):Me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),bd(e,t,r,o,n);case 7:return Me(e,t,t.pendingProps,n),t.child;case 8:return Me(e,t,t.pendingProps.children,n),t.child;case 12:return Me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ne(Zs,r._currentValue),r._currentValue=i,s!==null)if(yt(s.value,i)){if(s.children===o.children&&!Ve.current){t=Xt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){i=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=Wt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),xa(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(b(341));i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),xa(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,yr(t,n),o=ut(o),r=r(o),t.flags|=1,Me(e,t,r,n),t.child;case 14:return r=t.type,o=pt(r,t.pendingProps),o=pt(r.type,o),_d(e,t,r,o,n);case 15:return Em(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),Ps(e,t),t.tag=1,We(r)?(e=!0,qs(t)):e=!1,yr(t,n),xm(t,r,o),Sa(t,r,o,n),Na(null,t,r,!0,e,n);case 19:return Rm(e,t,n);case 22:return Cm(e,t,n)}throw Error(b(156,t.tag))};function Vm(e,t){return gp(e,t)}function Nw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function lt(e,t,n,r){return new Nw(e,t,n,r)}function Gu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function kw(e){if(typeof e=="function")return Gu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===pu)return 11;if(e===mu)return 14}return 2}function vn(e,t){var n=e.alternate;return n===null?(n=lt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function js(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Gu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case rr:return Mn(n.children,o,s,t);case fu:i=8,o|=8;break;case Hl:return e=lt(12,n,t,o|2),e.elementType=Hl,e.lanes=s,e;case Kl:return e=lt(13,n,t,o),e.elementType=Kl,e.lanes=s,e;case Ql:return e=lt(19,n,t,o),e.elementType=Ql,e.lanes=s,e;case Zf:return _i(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Yf:i=10;break e;case Jf:i=9;break e;case pu:i=11;break e;case mu:i=14;break e;case rn:i=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=lt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function Mn(e,t,n,r){return e=lt(7,e,r,t),e.lanes=n,e}function _i(e,t,n,r){return e=lt(22,e,r,t),e.elementType=Zf,e.lanes=n,e.stateNode={isHidden:!1},e}function kl(e,t,n){return e=lt(6,e,null,t),e.lanes=n,e}function Tl(e,t,n){return t=lt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Tw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=il(0),this.expirationTimes=il(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=il(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Xu(e,t,n,r,o,s,i,l,a){return e=new Tw(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=lt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Au(s),e}function Rw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:nr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Wm(e){if(!e)return xn;e=e._reactInternals;e:{if(Xn(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(We(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(We(n))return Wp(e,n,t)}return t}function Hm(e,t,n,r,o,s,i,l,a){return e=Xu(n,r,!0,e,o,s,i,l,a),e.context=Wm(null),n=e.current,r=Fe(),o=gn(n),s=Wt(r,o),s.callback=t??null,mn(n,s,o),e.current.lanes=o,zo(e,o,r),He(e,r),e}function ji(e,t,n,r){var o=t.current,s=Fe(),i=gn(o);return n=Wm(n),t.context===null?t.context=n:t.pendingContext=n,t=Wt(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=mn(o,t,i),e!==null&&(vt(e,o,i,s),ks(e,o,i)),i}function ui(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Bd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function qu(e,t){Bd(e,t),(e=e.alternate)&&Bd(e,t)}function Pw(){return null}var Km=typeof reportError=="function"?reportError:function(e){console.error(e)};function Yu(e){this._internalRoot=e}Ai.prototype.render=Yu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));ji(e,t,null,null)};Ai.prototype.unmount=Yu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Bn(function(){ji(null,e,null,null)}),t[Qt]=null}};function Ai(e){this._internalRoot=e}Ai.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sn.length&&t!==0&&t<sn[n].priority;n++);sn.splice(n,0,e),n===0&&kp(e)}};function Ju(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Oi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Vd(){}function bw(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=ui(i);s.call(u)}}var i=Hm(t,r,e,0,null,!1,!1,"",Vd);return e._reactRootContainer=i,e[Qt]=i.current,To(e.nodeType===8?e.parentNode:e),Bn(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=ui(a);l.call(u)}}var a=Xu(e,0,!1,null,null,!1,!1,"",Vd);return e._reactRootContainer=a,e[Qt]=a.current,To(e.nodeType===8?e.parentNode:e),Bn(function(){ji(t,a,n,r)}),a}function Li(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var l=o;o=function(){var a=ui(i);l.call(a)}}ji(t,i,e,o)}else i=bw(n,t,e,o,r);return ui(i)}Sp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=oo(t.pendingLanes);n!==0&&(vu(t,n|1),He(t,me()),!(X&6)&&(_r=me()+500,Nn()))}break;case 13:Bn(function(){var r=Gt(e,1);if(r!==null){var o=Fe();vt(r,e,1,o)}}),qu(e,1)}};yu=function(e){if(e.tag===13){var t=Gt(e,134217728);if(t!==null){var n=Fe();vt(t,e,134217728,n)}qu(e,134217728)}};Ep=function(e){if(e.tag===13){var t=gn(e),n=Gt(e,t);if(n!==null){var r=Fe();vt(n,e,t,r)}qu(e,t)}};Cp=function(){return Y};Np=function(e,t){var n=Y;try{return Y=e,t()}finally{Y=n}};ra=function(e,t,n){switch(t){case"input":if(ql(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ni(r);if(!o)throw Error(b(90));tp(r),ql(r,o)}}}break;case"textarea":rp(e,n);break;case"select":t=n.value,t!=null&&mr(e,!!n.multiple,t,!1)}};cp=Hu;dp=Bn;var _w={usingClientEntryPoint:!1,Events:[Uo,lr,Ni,ap,up,Hu]},Jr={findFiberByHostInstance:jn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},jw={bundleType:Jr.bundleType,version:Jr.version,rendererPackageName:Jr.rendererPackageName,rendererConfig:Jr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Jt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=mp(e),e===null?null:e.stateNode},findFiberByHostInstance:Jr.findFiberByHostInstance||Pw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fs.isDisabled&&fs.supportsFiber)try{wi=fs.inject(jw),At=fs}catch{}}rt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_w;rt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ju(t))throw Error(b(200));return Rw(e,t,null,n)};rt.createRoot=function(e,t){if(!Ju(e))throw Error(b(299));var n=!1,r="",o=Km;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Xu(e,1,!1,null,null,n,!1,r,o),e[Qt]=t.current,To(e.nodeType===8?e.parentNode:e),new Yu(t)};rt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=mp(t),e=e===null?null:e.stateNode,e};rt.flushSync=function(e){return Bn(e)};rt.hydrate=function(e,t,n){if(!Oi(t))throw Error(b(200));return Li(null,e,t,!0,n)};rt.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=Km;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Hm(t,null,e,1,n??null,o,!1,s,i),e[Qt]=t.current,To(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ai(t)};rt.render=function(e,t,n){if(!Oi(t))throw Error(b(200));return Li(null,e,t,!1,n)};rt.unmountComponentAtNode=function(e){if(!Oi(e))throw Error(b(40));return e._reactRootContainer?(Bn(function(){Li(null,null,e,!1,function(){e._reactRootContainer=null,e[Qt]=null})}),!0):!1};rt.unstable_batchedUpdates=Hu;rt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Oi(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return Li(e,t,n,!1,r)};rt.version="18.3.1-next-f1338f8080-20240426";function Qm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qm)}catch(e){console.error(e)}}Qm(),Qf.exports=rt;var qn=Qf.exports;const Aw=Lf(qn);var Wd=qn;Vl.createRoot=Wd.createRoot,Vl.hydrateRoot=Wd.hydrateRoot;function Hd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Gm(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Hd(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Hd(e[o],null)}}}}function fe(...e){return g.useCallback(Gm(...e),e)}function jr(e){const t=Lw(e),n=g.forwardRef((r,o)=>{const{children:s,...i}=r,l=g.Children.toArray(s),a=l.find(Mw);if(a){const u=a.props.children,f=l.map(m=>m===a?g.Children.count(u)>1?g.Children.only(null):g.isValidElement(u)?u.props.children:null:m);return c.jsx(t,{...i,ref:o,children:g.isValidElement(u)?g.cloneElement(u,void 0,f):null})}return c.jsx(t,{...i,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var Ow=jr("Slot");function Lw(e){const t=g.forwardRef((n,r)=>{const{children:o,...s}=n;if(g.isValidElement(o)){const i=Fw(o),l=Iw(s,o.props);return o.type!==g.Fragment&&(l.ref=r?Gm(r,i):i),g.cloneElement(o,l)}return g.Children.count(o)>1?g.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Dw=Symbol("radix.slottable");function Mw(e){return g.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Dw}function Iw(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...l)=>{const a=s(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Fw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Xm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Xm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function qm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Xm(e))&&(r&&(r+=" "),r+=t);return r}const Kd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Qd=qm,Di=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Qd(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],m=s==null?void 0:s[u];if(f===null)return null;const y=Kd(f)||Kd(m);return o[u][y]}),l=n&&Object.entries(n).reduce((u,f)=>{let[m,y]=f;return y===void 0||(u[m]=y),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:m,className:y,...w}=f;return Object.entries(w).every(S=>{let[p,x]=S;return Array.isArray(x)?x.includes({...s,...l}[p]):{...s,...l}[p]===x})?[...u,m,y]:u},[]);return Qd(e,i,a,n==null?void 0:n.class,n==null?void 0:n.className)},Zu="-",zw=e=>{const t=Uw(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const l=i.split(Zu);return l[0]===""&&l.length!==1&&l.shift(),Ym(l,t)||$w(i)},getConflictingClassGroupIds:(i,l)=>{const a=n[i]||[];return l&&r[i]?[...a,...r[i]]:a}}},Ym=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Ym(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(Zu);return(i=t.validators.find(({validator:l})=>l(s)))==null?void 0:i.classGroupId},Gd=/^\[(.+)\]$/,$w=e=>{if(Gd.test(e)){const t=Gd.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Uw=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Vw(Object.entries(e.classGroups),n).forEach(([s,i])=>{Ma(i,r,s,t)}),r},Ma=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Xd(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(Bw(o)){Ma(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Ma(i,Xd(t,s),n,r)})})},Xd=(e,t)=>{let n=e;return t.split(Zu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Bw=e=>e.isThemeGetter,Vw=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,l])=>[t+i,l])):s);return[n,o]}):e,Ww=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Jm="!",Hw=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=l=>{const a=[];let u=0,f=0,m;for(let x=0;x<l.length;x++){let h=l[x];if(u===0){if(h===o&&(r||l.slice(x,x+s)===t)){a.push(l.slice(f,x)),f=x+s;continue}if(h==="/"){m=x;continue}}h==="["?u++:h==="]"&&u--}const y=a.length===0?l:l.substring(f),w=y.startsWith(Jm),S=w?y.substring(1):y,p=m&&m>f?m-f:void 0;return{modifiers:a,hasImportantModifier:w,baseClassName:S,maybePostfixModifierPosition:p}};return n?l=>n({className:l,parseClassName:i}):i},Kw=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Qw=e=>({cache:Ww(e.cacheSize),parseClassName:Hw(e),...zw(e)}),Gw=/\s+/,Xw=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(Gw);let l="";for(let a=i.length-1;a>=0;a-=1){const u=i[a],{modifiers:f,hasImportantModifier:m,baseClassName:y,maybePostfixModifierPosition:w}=n(u);let S=!!w,p=r(S?y.substring(0,w):y);if(!p){if(!S){l=u+(l.length>0?" "+l:l);continue}if(p=r(y),!p){l=u+(l.length>0?" "+l:l);continue}S=!1}const x=Kw(f).join(":"),h=m?x+Jm:x,d=h+p;if(s.includes(d))continue;s.push(d);const v=o(p,S);for(let E=0;E<v.length;++E){const C=v[E];s.push(h+C)}l=u+(l.length>0?" "+l:l)}return l};function qw(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Zm(t))&&(r&&(r+=" "),r+=n);return r}const Zm=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Zm(e[r]))&&(n&&(n+=" "),n+=t);return n};function Yw(e,...t){let n,r,o,s=i;function i(a){const u=t.reduce((f,m)=>m(f),e());return n=Qw(u),r=n.cache.get,o=n.cache.set,s=l,l(a)}function l(a){const u=r(a);if(u)return u;const f=Xw(a,n);return o(a,f),f}return function(){return s(qw.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},eh=/^\[(?:([a-z-]+):)?(.+)\]$/i,Jw=/^\d+\/\d+$/,Zw=new Set(["px","full","screen"]),e0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,t0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,n0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,r0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,o0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,It=e=>wr(e)||Zw.has(e)||Jw.test(e),en=e=>Mr(e,"length",f0),wr=e=>!!e&&!Number.isNaN(Number(e)),Rl=e=>Mr(e,"number",wr),Zr=e=>!!e&&Number.isInteger(Number(e)),s0=e=>e.endsWith("%")&&wr(e.slice(0,-1)),H=e=>eh.test(e),tn=e=>e0.test(e),i0=new Set(["length","size","percentage"]),l0=e=>Mr(e,i0,th),a0=e=>Mr(e,"position",th),u0=new Set(["image","url"]),c0=e=>Mr(e,u0,m0),d0=e=>Mr(e,"",p0),eo=()=>!0,Mr=(e,t,n)=>{const r=eh.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},f0=e=>t0.test(e)&&!n0.test(e),th=()=>!1,p0=e=>r0.test(e),m0=e=>o0.test(e),h0=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),s=se("borderRadius"),i=se("borderSpacing"),l=se("borderWidth"),a=se("contrast"),u=se("grayscale"),f=se("hueRotate"),m=se("invert"),y=se("gap"),w=se("gradientColorStops"),S=se("gradientColorStopPositions"),p=se("inset"),x=se("margin"),h=se("opacity"),d=se("padding"),v=se("saturate"),E=se("scale"),C=se("sepia"),T=se("skew"),N=se("space"),R=se("translate"),j=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],I=()=>["auto",H,t],L=()=>[H,t],B=()=>["",It,en],O=()=>["auto",wr,H],$=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],F=()=>["solid","dashed","dotted","double","none"],W=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],A=()=>["","0",H],M=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>[wr,H];return{cacheSize:500,separator:":",theme:{colors:[eo],spacing:[It,en],blur:["none","",tn,H],brightness:U(),borderColor:[e],borderRadius:["none","","full",tn,H],borderSpacing:L(),borderWidth:B(),contrast:U(),grayscale:A(),hueRotate:U(),invert:A(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[s0,en],inset:I(),margin:I(),opacity:U(),padding:L(),saturate:U(),scale:U(),sepia:A(),skew:U(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[tn]}],"break-after":[{"break-after":M()}],"break-before":[{"break-before":M()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...$(),H]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Zr,H]}],basis:[{basis:I()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",Zr,H]}],"grid-cols":[{"grid-cols":[eo]}],"col-start-end":[{col:["auto",{span:["full",Zr,H]},H]}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":[eo]}],"row-start-end":[{row:["auto",{span:[Zr,H]},H]}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[y]}],"gap-x":[{"gap-x":[y]}],"gap-y":[{"gap-y":[y]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[d]}],px:[{px:[d]}],py:[{py:[d]}],ps:[{ps:[d]}],pe:[{pe:[d]}],pt:[{pt:[d]}],pr:[{pr:[d]}],pb:[{pb:[d]}],pl:[{pl:[d]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[tn]},tn]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tn,en]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Rl]}],"font-family":[{font:[eo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",wr,Rl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",It,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...F(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",It,en]}],"underline-offset":[{"underline-offset":["auto",It,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...$(),a0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",l0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},c0]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[S]}],"gradient-via-pos":[{via:[S]}],"gradient-to-pos":[{to:[S]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...F(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:F()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...F()]}],"outline-offset":[{"outline-offset":[It,H]}],"outline-w":[{outline:[It,en]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[It,en]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tn,d0]}],"shadow-color":[{shadow:[eo]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...W(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":W()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",tn,H]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[m]}],saturate:[{saturate:[v]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:U()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:U()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[E]}],"scale-x":[{"scale-x":[E]}],"scale-y":[{"scale-y":[E]}],rotate:[{rotate:[Zr,H]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[It,en,Rl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},g0=Yw(h0);function K(...e){return g0(qm(e))}function Ia(e){return e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):""}function v0(e,t){return!e||e.length<=t?e:e.substring(0,t)+"..."}const y0=Di("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Ee=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?Ow:"button";return c.jsx(i,{className:K(y0({variant:t,size:n,className:e})),ref:s,...o})});Ee.displayName="Button";const jt=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));jt.displayName="Card";const nh=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("flex flex-col space-y-1.5 p-6",e),...t}));nh.displayName="CardHeader";const x0=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("text-2xl font-semibold leading-none tracking-tight",e),...t}));x0.displayName="CardTitle";const w0=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("text-sm text-muted-foreground",e),...t}));w0.displayName="CardDescription";const Vt=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("p-6 pt-0",e),...t}));Vt.displayName="CardContent";const rh=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:K("flex items-center p-6 pt-0",e),...t}));rh.displayName="CardFooter";function oh(e,t){return function(){return e.apply(t,arguments)}}const{toString:S0}=Object.prototype,{getPrototypeOf:ec}=Object,{iterator:Mi,toStringTag:sh}=Symbol,Ii=(e=>t=>{const n=S0.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Et=e=>(e=e.toLowerCase(),t=>Ii(t)===e),Fi=e=>t=>typeof t===e,{isArray:Ir}=Array,Do=Fi("undefined");function E0(e){return e!==null&&!Do(e)&&e.constructor!==null&&!Do(e.constructor)&&Ke(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ih=Et("ArrayBuffer");function C0(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ih(e.buffer),t}const N0=Fi("string"),Ke=Fi("function"),lh=Fi("number"),zi=e=>e!==null&&typeof e=="object",k0=e=>e===!0||e===!1,As=e=>{if(Ii(e)!=="object")return!1;const t=ec(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(sh in e)&&!(Mi in e)},T0=Et("Date"),R0=Et("File"),P0=Et("Blob"),b0=Et("FileList"),_0=e=>zi(e)&&Ke(e.pipe),j0=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ke(e.append)&&((t=Ii(e))==="formdata"||t==="object"&&Ke(e.toString)&&e.toString()==="[object FormData]"))},A0=Et("URLSearchParams"),[O0,L0,D0,M0]=["ReadableStream","Request","Response","Headers"].map(Et),I0=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Vo(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),Ir(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let l;for(r=0;r<i;r++)l=s[r],t.call(null,e[l],l,e)}}function ah(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const Ln=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,uh=e=>!Do(e)&&e!==Ln;function Fa(){const{caseless:e}=uh(this)&&this||{},t={},n=(r,o)=>{const s=e&&ah(t,o)||o;As(t[s])&&As(r)?t[s]=Fa(t[s],r):As(r)?t[s]=Fa({},r):Ir(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Vo(arguments[r],n);return t}const F0=(e,t,n,{allOwnKeys:r}={})=>(Vo(t,(o,s)=>{n&&Ke(o)?e[s]=oh(o,n):e[s]=o},{allOwnKeys:r}),e),z0=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),$0=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},U0=(e,t,n,r)=>{let o,s,i;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&ec(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},B0=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},V0=e=>{if(!e)return null;if(Ir(e))return e;let t=e.length;if(!lh(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},W0=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ec(Uint8Array)),H0=(e,t)=>{const r=(e&&e[Mi]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},K0=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Q0=Et("HTMLFormElement"),G0=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),qd=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),X0=Et("RegExp"),ch=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Vo(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},q0=e=>{ch(e,(t,n)=>{if(Ke(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Ke(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Y0=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return Ir(e)?r(e):r(String(e).split(t)),n},J0=()=>{},Z0=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function e1(e){return!!(e&&Ke(e.append)&&e[sh]==="FormData"&&e[Mi])}const t1=e=>{const t=new Array(10),n=(r,o)=>{if(zi(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=Ir(r)?[]:{};return Vo(r,(i,l)=>{const a=n(i,o+1);!Do(a)&&(s[l]=a)}),t[o]=void 0,s}}return r};return n(e,0)},n1=Et("AsyncFunction"),r1=e=>e&&(zi(e)||Ke(e))&&Ke(e.then)&&Ke(e.catch),dh=((e,t)=>e?setImmediate:t?((n,r)=>(Ln.addEventListener("message",({source:o,data:s})=>{o===Ln&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),Ln.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ke(Ln.postMessage)),o1=typeof queueMicrotask<"u"?queueMicrotask.bind(Ln):typeof process<"u"&&process.nextTick||dh,s1=e=>e!=null&&Ke(e[Mi]),k={isArray:Ir,isArrayBuffer:ih,isBuffer:E0,isFormData:j0,isArrayBufferView:C0,isString:N0,isNumber:lh,isBoolean:k0,isObject:zi,isPlainObject:As,isReadableStream:O0,isRequest:L0,isResponse:D0,isHeaders:M0,isUndefined:Do,isDate:T0,isFile:R0,isBlob:P0,isRegExp:X0,isFunction:Ke,isStream:_0,isURLSearchParams:A0,isTypedArray:W0,isFileList:b0,forEach:Vo,merge:Fa,extend:F0,trim:I0,stripBOM:z0,inherits:$0,toFlatObject:U0,kindOf:Ii,kindOfTest:Et,endsWith:B0,toArray:V0,forEachEntry:H0,matchAll:K0,isHTMLForm:Q0,hasOwnProperty:qd,hasOwnProp:qd,reduceDescriptors:ch,freezeMethods:q0,toObjectSet:Y0,toCamelCase:G0,noop:J0,toFiniteNumber:Z0,findKey:ah,global:Ln,isContextDefined:uh,isSpecCompliantForm:e1,toJSONObject:t1,isAsyncFn:n1,isThenable:r1,setImmediate:dh,asap:o1,isIterable:s1};function V(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}k.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:k.toJSONObject(this.config),code:this.code,status:this.status}}});const fh=V.prototype,ph={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ph[e]={value:e}});Object.defineProperties(V,ph);Object.defineProperty(fh,"isAxiosError",{value:!0});V.from=(e,t,n,r,o,s)=>{const i=Object.create(fh);return k.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),V.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const i1=null;function za(e){return k.isPlainObject(e)||k.isArray(e)}function mh(e){return k.endsWith(e,"[]")?e.slice(0,-2):e}function Yd(e,t,n){return e?e.concat(t).map(function(o,s){return o=mh(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function l1(e){return k.isArray(e)&&!e.some(za)}const a1=k.toFlatObject(k,{},null,function(t){return/^is[A-Z]/.test(t)});function $i(e,t,n){if(!k.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=k.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,x){return!k.isUndefined(x[p])});const r=n.metaTokens,o=n.visitor||f,s=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&k.isSpecCompliantForm(t);if(!k.isFunction(o))throw new TypeError("visitor must be a function");function u(S){if(S===null)return"";if(k.isDate(S))return S.toISOString();if(!a&&k.isBlob(S))throw new V("Blob is not supported. Use a Buffer instead.");return k.isArrayBuffer(S)||k.isTypedArray(S)?a&&typeof Blob=="function"?new Blob([S]):Buffer.from(S):S}function f(S,p,x){let h=S;if(S&&!x&&typeof S=="object"){if(k.endsWith(p,"{}"))p=r?p:p.slice(0,-2),S=JSON.stringify(S);else if(k.isArray(S)&&l1(S)||(k.isFileList(S)||k.endsWith(p,"[]"))&&(h=k.toArray(S)))return p=mh(p),h.forEach(function(v,E){!(k.isUndefined(v)||v===null)&&t.append(i===!0?Yd([p],E,s):i===null?p:p+"[]",u(v))}),!1}return za(S)?!0:(t.append(Yd(x,p,s),u(S)),!1)}const m=[],y=Object.assign(a1,{defaultVisitor:f,convertValue:u,isVisitable:za});function w(S,p){if(!k.isUndefined(S)){if(m.indexOf(S)!==-1)throw Error("Circular reference detected in "+p.join("."));m.push(S),k.forEach(S,function(h,d){(!(k.isUndefined(h)||h===null)&&o.call(t,h,k.isString(d)?d.trim():d,p,y))===!0&&w(h,p?p.concat(d):[d])}),m.pop()}}if(!k.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Jd(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function tc(e,t){this._pairs=[],e&&$i(e,this,t)}const hh=tc.prototype;hh.append=function(t,n){this._pairs.push([t,n])};hh.toString=function(t){const n=t?function(r){return t.call(this,r,Jd)}:Jd;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function u1(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function gh(e,t,n){if(!t)return e;const r=n&&n.encode||u1;k.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=k.isURLSearchParams(t)?t.toString():new tc(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Zd{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){k.forEach(this.handlers,function(r){r!==null&&t(r)})}}const vh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},c1=typeof URLSearchParams<"u"?URLSearchParams:tc,d1=typeof FormData<"u"?FormData:null,f1=typeof Blob<"u"?Blob:null,p1={isBrowser:!0,classes:{URLSearchParams:c1,FormData:d1,Blob:f1},protocols:["http","https","file","blob","url","data"]},nc=typeof window<"u"&&typeof document<"u",$a=typeof navigator=="object"&&navigator||void 0,m1=nc&&(!$a||["ReactNative","NativeScript","NS"].indexOf($a.product)<0),h1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",g1=nc&&window.location.href||"http://localhost",v1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:nc,hasStandardBrowserEnv:m1,hasStandardBrowserWebWorkerEnv:h1,navigator:$a,origin:g1},Symbol.toStringTag,{value:"Module"})),Ae={...v1,...p1};function y1(e,t){return $i(e,new Ae.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return Ae.isNode&&k.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function x1(e){return k.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function w1(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function yh(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=s>=n.length;return i=!i&&k.isArray(o)?o.length:i,a?(k.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!l):((!o[i]||!k.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&k.isArray(o[i])&&(o[i]=w1(o[i])),!l)}if(k.isFormData(e)&&k.isFunction(e.entries)){const n={};return k.forEachEntry(e,(r,o)=>{t(x1(r),o,n,0)}),n}return null}function S1(e,t,n){if(k.isString(e))try{return(t||JSON.parse)(e),k.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Wo={transitional:vh,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=k.isObject(t);if(s&&k.isHTMLForm(t)&&(t=new FormData(t)),k.isFormData(t))return o?JSON.stringify(yh(t)):t;if(k.isArrayBuffer(t)||k.isBuffer(t)||k.isStream(t)||k.isFile(t)||k.isBlob(t)||k.isReadableStream(t))return t;if(k.isArrayBufferView(t))return t.buffer;if(k.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return y1(t,this.formSerializer).toString();if((l=k.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return $i(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),S1(t)):t}],transformResponse:[function(t){const n=this.transitional||Wo.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(k.isResponse(t)||k.isReadableStream(t))return t;if(t&&k.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?V.from(l,V.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ae.classes.FormData,Blob:Ae.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};k.forEach(["delete","get","head","post","put","patch"],e=>{Wo.headers[e]={}});const E1=k.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),C1=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&E1[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},ef=Symbol("internals");function to(e){return e&&String(e).trim().toLowerCase()}function Os(e){return e===!1||e==null?e:k.isArray(e)?e.map(Os):String(e)}function N1(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const k1=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Pl(e,t,n,r,o){if(k.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!k.isString(t)){if(k.isString(r))return t.indexOf(r)!==-1;if(k.isRegExp(r))return r.test(t)}}function T1(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function R1(e,t){const n=k.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}let Qe=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(l,a,u){const f=to(a);if(!f)throw new Error("header name must be a non-empty string");const m=k.findKey(o,f);(!m||o[m]===void 0||u===!0||u===void 0&&o[m]!==!1)&&(o[m||a]=Os(l))}const i=(l,a)=>k.forEach(l,(u,f)=>s(u,f,a));if(k.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(k.isString(t)&&(t=t.trim())&&!k1(t))i(C1(t),n);else if(k.isObject(t)&&k.isIterable(t)){let l={},a,u;for(const f of t){if(!k.isArray(f))throw TypeError("Object iterator must return a key-value pair");l[u=f[0]]=(a=l[u])?k.isArray(a)?[...a,f[1]]:[a,f[1]]:f[1]}i(l,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=to(t),t){const r=k.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return N1(o);if(k.isFunction(n))return n.call(this,o,r);if(k.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=to(t),t){const r=k.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Pl(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=to(i),i){const l=k.findKey(r,i);l&&(!n||Pl(r,r[l],l,n))&&(delete r[l],o=!0)}}return k.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||Pl(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return k.forEach(this,(o,s)=>{const i=k.findKey(r,s);if(i){n[i]=Os(o),delete n[s];return}const l=t?T1(s):String(s).trim();l!==s&&delete n[s],n[l]=Os(o),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return k.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&k.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[ef]=this[ef]={accessors:{}}).accessors,o=this.prototype;function s(i){const l=to(i);r[l]||(R1(o,i),r[l]=!0)}return k.isArray(t)?t.forEach(s):s(t),this}};Qe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);k.reduceDescriptors(Qe.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});k.freezeMethods(Qe);function bl(e,t){const n=this||Wo,r=t||n,o=Qe.from(r.headers);let s=r.data;return k.forEach(e,function(l){s=l.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function xh(e){return!!(e&&e.__CANCEL__)}function Fr(e,t,n){V.call(this,e??"canceled",V.ERR_CANCELED,t,n),this.name="CanceledError"}k.inherits(Fr,V,{__CANCEL__:!0});function wh(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new V("Request failed with status code "+n.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function P1(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function b1(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),f=r[s];i||(i=u),n[o]=a,r[o]=u;let m=s,y=0;for(;m!==o;)y+=n[m++],m=m%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-i<t)return;const w=f&&u-f;return w?Math.round(y*1e3/w):void 0}}function _1(e,t){let n=0,r=1e3/t,o,s;const i=(u,f=Date.now())=>{n=f,o=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const f=Date.now(),m=f-n;m>=r?i(u,f):(o=u,s||(s=setTimeout(()=>{s=null,i(o)},r-m)))},()=>o&&i(o)]}const ci=(e,t,n=3)=>{let r=0;const o=b1(50,250);return _1(s=>{const i=s.loaded,l=s.lengthComputable?s.total:void 0,a=i-r,u=o(a),f=i<=l;r=i;const m={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&f?(l-i)/u:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(m)},n)},tf=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},nf=e=>(...t)=>k.asap(()=>e(...t)),j1=Ae.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ae.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ae.origin),Ae.navigator&&/(msie|trident)/i.test(Ae.navigator.userAgent)):()=>!0,A1=Ae.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];k.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),k.isString(r)&&i.push("path="+r),k.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function O1(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function L1(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Sh(e,t,n){let r=!O1(t);return e&&(r||n==!1)?L1(e,t):t}const rf=e=>e instanceof Qe?{...e}:e;function Vn(e,t){t=t||{};const n={};function r(u,f,m,y){return k.isPlainObject(u)&&k.isPlainObject(f)?k.merge.call({caseless:y},u,f):k.isPlainObject(f)?k.merge({},f):k.isArray(f)?f.slice():f}function o(u,f,m,y){if(k.isUndefined(f)){if(!k.isUndefined(u))return r(void 0,u,m,y)}else return r(u,f,m,y)}function s(u,f){if(!k.isUndefined(f))return r(void 0,f)}function i(u,f){if(k.isUndefined(f)){if(!k.isUndefined(u))return r(void 0,u)}else return r(void 0,f)}function l(u,f,m){if(m in t)return r(u,f);if(m in e)return r(void 0,u)}const a={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,f,m)=>o(rf(u),rf(f),m,!0)};return k.forEach(Object.keys(Object.assign({},e,t)),function(f){const m=a[f]||o,y=m(e[f],t[f],f);k.isUndefined(y)&&m!==l||(n[f]=y)}),n}const Eh=e=>{const t=Vn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:l}=t;t.headers=i=Qe.from(i),t.url=gh(Sh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(k.isFormData(n)){if(Ae.hasStandardBrowserEnv||Ae.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...f]=a?a.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...f].join("; "))}}if(Ae.hasStandardBrowserEnv&&(r&&k.isFunction(r)&&(r=r(t)),r||r!==!1&&j1(t.url))){const u=o&&s&&A1.read(s);u&&i.set(o,u)}return t},D1=typeof XMLHttpRequest<"u",M1=D1&&function(e){return new Promise(function(n,r){const o=Eh(e);let s=o.data;const i=Qe.from(o.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=o,f,m,y,w,S;function p(){w&&w(),S&&S(),o.cancelToken&&o.cancelToken.unsubscribe(f),o.signal&&o.signal.removeEventListener("abort",f)}let x=new XMLHttpRequest;x.open(o.method.toUpperCase(),o.url,!0),x.timeout=o.timeout;function h(){if(!x)return;const v=Qe.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),C={data:!l||l==="text"||l==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:v,config:e,request:x};wh(function(N){n(N),p()},function(N){r(N),p()},C),x=null}"onloadend"in x?x.onloadend=h:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(h)},x.onabort=function(){x&&(r(new V("Request aborted",V.ECONNABORTED,e,x)),x=null)},x.onerror=function(){r(new V("Network Error",V.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let E=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const C=o.transitional||vh;o.timeoutErrorMessage&&(E=o.timeoutErrorMessage),r(new V(E,C.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,x)),x=null},s===void 0&&i.setContentType(null),"setRequestHeader"in x&&k.forEach(i.toJSON(),function(E,C){x.setRequestHeader(C,E)}),k.isUndefined(o.withCredentials)||(x.withCredentials=!!o.withCredentials),l&&l!=="json"&&(x.responseType=o.responseType),u&&([y,S]=ci(u,!0),x.addEventListener("progress",y)),a&&x.upload&&([m,w]=ci(a),x.upload.addEventListener("progress",m),x.upload.addEventListener("loadend",w)),(o.cancelToken||o.signal)&&(f=v=>{x&&(r(!v||v.type?new Fr(null,e,x):v),x.abort(),x=null)},o.cancelToken&&o.cancelToken.subscribe(f),o.signal&&(o.signal.aborted?f():o.signal.addEventListener("abort",f)));const d=P1(o.url);if(d&&Ae.protocols.indexOf(d)===-1){r(new V("Unsupported protocol "+d+":",V.ERR_BAD_REQUEST,e));return}x.send(s||null)})},I1=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(u){if(!o){o=!0,l();const f=u instanceof Error?u:this.reason;r.abort(f instanceof V?f:new Fr(f instanceof Error?f.message:f))}};let i=t&&setTimeout(()=>{i=null,s(new V(`timeout ${t} of ms exceeded`,V.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:a}=r;return a.unsubscribe=()=>k.asap(l),a}},F1=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},z1=async function*(e,t){for await(const n of $1(e))yield*F1(n,t)},$1=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},of=(e,t,n,r)=>{const o=z1(e,t);let s=0,i,l=a=>{i||(i=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:f}=await o.next();if(u){l(),a.close();return}let m=f.byteLength;if(n){let y=s+=m;n(y)}a.enqueue(new Uint8Array(f))}catch(u){throw l(u),u}},cancel(a){return l(a),o.return()}},{highWaterMark:2})},Ui=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ch=Ui&&typeof ReadableStream=="function",U1=Ui&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Nh=(e,...t)=>{try{return!!e(...t)}catch{return!1}},B1=Ch&&Nh(()=>{let e=!1;const t=new Request(Ae.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),sf=64*1024,Ua=Ch&&Nh(()=>k.isReadableStream(new Response("").body)),di={stream:Ua&&(e=>e.body)};Ui&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!di[t]&&(di[t]=k.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new V(`Response type '${t}' is not supported`,V.ERR_NOT_SUPPORT,r)})})})(new Response);const V1=async e=>{if(e==null)return 0;if(k.isBlob(e))return e.size;if(k.isSpecCompliantForm(e))return(await new Request(Ae.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(k.isArrayBufferView(e)||k.isArrayBuffer(e))return e.byteLength;if(k.isURLSearchParams(e)&&(e=e+""),k.isString(e))return(await U1(e)).byteLength},W1=async(e,t)=>{const n=k.toFiniteNumber(e.getContentLength());return n??V1(t)},H1=Ui&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:f,withCredentials:m="same-origin",fetchOptions:y}=Eh(e);u=u?(u+"").toLowerCase():"text";let w=I1([o,s&&s.toAbortSignal()],i),S;const p=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let x;try{if(a&&B1&&n!=="get"&&n!=="head"&&(x=await W1(f,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(k.isFormData(r)&&(T=C.headers.get("content-type"))&&f.setContentType(T),C.body){const[N,R]=tf(x,ci(nf(a)));r=of(C.body,sf,N,R)}}k.isString(m)||(m=m?"include":"omit");const h="credentials"in Request.prototype;S=new Request(t,{...y,signal:w,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:h?m:void 0});let d=await fetch(S);const v=Ua&&(u==="stream"||u==="response");if(Ua&&(l||v&&p)){const C={};["status","statusText","headers"].forEach(j=>{C[j]=d[j]});const T=k.toFiniteNumber(d.headers.get("content-length")),[N,R]=l&&tf(T,ci(nf(l),!0))||[];d=new Response(of(d.body,sf,N,()=>{R&&R(),p&&p()}),C)}u=u||"text";let E=await di[k.findKey(di,u)||"text"](d,e);return!v&&p&&p(),await new Promise((C,T)=>{wh(C,T,{data:E,headers:Qe.from(d.headers),status:d.status,statusText:d.statusText,config:e,request:S})})}catch(h){throw p&&p(),h&&h.name==="TypeError"&&/Load failed|fetch/i.test(h.message)?Object.assign(new V("Network Error",V.ERR_NETWORK,e,S),{cause:h.cause||h}):V.from(h,h&&h.code,e,S)}}),Ba={http:i1,xhr:M1,fetch:H1};k.forEach(Ba,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const lf=e=>`- ${e}`,K1=e=>k.isFunction(e)||e===null||e===!1,kh={getAdapter:e=>{e=k.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!K1(n)&&(r=Ba[(i=String(n)).toLowerCase()],r===void 0))throw new V(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(lf).join(`
`):" "+lf(s[0]):"as no adapter specified";throw new V("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Ba};function _l(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Fr(null,e)}function af(e){return _l(e),e.headers=Qe.from(e.headers),e.data=bl.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),kh.getAdapter(e.adapter||Wo.adapter)(e).then(function(r){return _l(e),r.data=bl.call(e,e.transformResponse,r),r.headers=Qe.from(r.headers),r},function(r){return xh(r)||(_l(e),r&&r.response&&(r.response.data=bl.call(e,e.transformResponse,r.response),r.response.headers=Qe.from(r.response.headers))),Promise.reject(r)})}const Th="1.9.0",Bi={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Bi[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const uf={};Bi.transitional=function(t,n,r){function o(s,i){return"[Axios v"+Th+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,l)=>{if(t===!1)throw new V(o(i," has been removed"+(n?" in "+n:"")),V.ERR_DEPRECATED);return n&&!uf[i]&&(uf[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,l):!0}};Bi.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Q1(e,t,n){if(typeof e!="object")throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const l=e[s],a=l===void 0||i(l,s,e);if(a!==!0)throw new V("option "+s+" must be "+a,V.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new V("Unknown option "+s,V.ERR_BAD_OPTION)}}const Ls={assertOptions:Q1,validators:Bi},Rt=Ls.validators;let In=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Zd,response:new Zd}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Vn(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&Ls.assertOptions(r,{silentJSONParsing:Rt.transitional(Rt.boolean),forcedJSONParsing:Rt.transitional(Rt.boolean),clarifyTimeoutError:Rt.transitional(Rt.boolean)},!1),o!=null&&(k.isFunction(o)?n.paramsSerializer={serialize:o}:Ls.assertOptions(o,{encode:Rt.function,serialize:Rt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ls.assertOptions(n,{baseUrl:Rt.spelling("baseURL"),withXsrfToken:Rt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&k.merge(s.common,s[n.method]);s&&k.forEach(["delete","get","head","post","put","patch","common"],S=>{delete s[S]}),n.headers=Qe.concat(i,s);const l=[];let a=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(n)===!1||(a=a&&p.synchronous,l.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let f,m=0,y;if(!a){const S=[af.bind(this),void 0];for(S.unshift.apply(S,l),S.push.apply(S,u),y=S.length,f=Promise.resolve(n);m<y;)f=f.then(S[m++],S[m++]);return f}y=l.length;let w=n;for(m=0;m<y;){const S=l[m++],p=l[m++];try{w=S(w)}catch(x){p.call(this,x);break}}try{f=af.call(this,w)}catch(S){return Promise.reject(S)}for(m=0,y=u.length;m<y;)f=f.then(u[m++],u[m++]);return f}getUri(t){t=Vn(this.defaults,t);const n=Sh(t.baseURL,t.url,t.allowAbsoluteUrls);return gh(n,t.params,t.paramsSerializer)}};k.forEach(["delete","get","head","options"],function(t){In.prototype[t]=function(n,r){return this.request(Vn(r||{},{method:t,url:n,data:(r||{}).data}))}});k.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,l){return this.request(Vn(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}In.prototype[t]=n(),In.prototype[t+"Form"]=n(!0)});let G1=class Rh{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(l=>{r.subscribe(l),s=l}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,l){r.reason||(r.reason=new Fr(s,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Rh(function(o){t=o}),cancel:t}}};function X1(e){return function(n){return e.apply(null,n)}}function q1(e){return k.isObject(e)&&e.isAxiosError===!0}const Va={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Va).forEach(([e,t])=>{Va[t]=e});function Ph(e){const t=new In(e),n=oh(In.prototype.request,t);return k.extend(n,In.prototype,t,{allOwnKeys:!0}),k.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return Ph(Vn(e,o))},n}const he=Ph(Wo);he.Axios=In;he.CanceledError=Fr;he.CancelToken=G1;he.isCancel=xh;he.VERSION=Th;he.toFormData=$i;he.AxiosError=V;he.Cancel=he.CanceledError;he.all=function(t){return Promise.all(t)};he.spread=X1;he.isAxiosError=q1;he.mergeConfig=Vn;he.AxiosHeaders=Qe;he.formToJSON=e=>yh(k.isHTMLForm(e)?new FormData(e):e);he.getAdapter=kh.getAdapter;he.HttpStatusCode=Va;he.default=he;const{Axios:yk,AxiosError:xk,CanceledError:wk,isCancel:Sk,CancelToken:Ek,VERSION:Ck,all:Nk,Cancel:kk,isAxiosError:Tk,spread:Rk,toFormData:Pk,AxiosHeaders:bk,HttpStatusCode:_k,formToJSON:jk,getAdapter:Ak,mergeConfig:Ok}=he,Y1="/api",qe=he.create({baseURL:Y1,headers:{"Content-Type":"application/json"}});qe.interceptors.request.use(e=>{var t;return console.log(`API Request: ${(t=e.method)==null?void 0:t.toUpperCase()} ${e.url}`),e},e=>(console.error("API Request Error:",e),Promise.reject(e)));qe.interceptors.response.use(e=>(console.log(`API Response: ${e.status} ${e.config.url}`),e),e=>{var t;return console.error("API Response Error:",((t=e.response)==null?void 0:t.data)||e.message),Promise.reject(e)});const tr={getAll:e=>{const t=new URLSearchParams;return e&&Object.entries(e).forEach(([n,r])=>{r!==""&&r!==null&&r!==void 0&&(Array.isArray(r)?r.forEach(o=>t.append(n,o.toString())):t.append(n,r.toString()))}),qe.get(`/jobs?${t.toString()}`)},getById:e=>qe.get(`/jobs/${e}`),create:e=>qe.post("/jobs",e),update:(e,t)=>qe.put(`/jobs/${e}`,t),delete:e=>qe.delete(`/jobs/${e}`),exportCSV:()=>qe.get("/jobs/export",{responseType:"blob"}),getStatistics:()=>qe.get("/statistics")},cf={getAll:()=>qe.get("/tags"),create:e=>qe.post("/tags",e),update:(e,t)=>qe.put(`/tags/${e}`,t),delete:e=>qe.delete(`/tags/${e}`)};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var J1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),re=(e,t)=>{const n=g.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:l="",children:a,...u},f)=>g.createElement("svg",{ref:f,...J1,width:o,height:o,stroke:r,strokeWidth:i?Number(s)*24/Number(o):s,className:["lucide",`lucide-${Z1(e)}`,l].join(" "),...u},[...t.map(([m,y])=>g.createElement(m,y)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eS=re("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rc=re("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oc=re("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tS=re("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bh=re("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nS=re("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _h=re("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=re("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=re("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oS=re("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sS=re("FileX",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"9.5",x2:"14.5",y1:"12.5",y2:"17.5",key:"izs6du"}],["line",{x1:"14.5",x2:"9.5",y1:"12.5",y2:"17.5",key:"1lehlj"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iS=re("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=re("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jh=re("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ic=re("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ah=re("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=re("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lh=re("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aS=re("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=re("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dh=re("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mh=re("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cS=re("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ih=re("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dS=re("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lc=re("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),fS=({onNewJob:e,onExportCSV:t,loading:n})=>{const{view:r,setView:o,jobs:s}=el(),[i,l]=g.useState(null);g.useEffect(()=>{a()},[s]);const a=async()=>{try{const u=await tr.getStatistics();l(u.data)}catch(u){console.error("Failed to load statistics:",u)}};return c.jsx("header",{className:"bg-card border-b border-border shadow-sm",children:c.jsx("div",{className:"container mx-auto mobile-padding",children:c.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between py-6 gap-4",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:"Job Application Tracker"}),c.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Manage and track your job applications efficiently"})]}),i&&c.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3",children:[c.jsx(jt,{className:"p-3",children:c.jsx(Vt,{className:"p-0",children:c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:c.jsx(cS,{className:"h-4 w-4 text-primary"})}),c.jsxs("div",{children:[c.jsx("p",{className:"text-xs text-muted-foreground",children:"Total"}),c.jsx("p",{className:"text-lg font-semibold",children:i.total_applications})]})]})})}),c.jsx(jt,{className:"p-3",children:c.jsx(Vt,{className:"p-0",children:c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:c.jsx(oc,{className:"h-4 w-4 text-blue-600"})}),c.jsxs("div",{children:[c.jsx("p",{className:"text-xs text-muted-foreground",children:"This Month"}),c.jsx("p",{className:"text-lg font-semibold",children:i.applications_this_month})]})]})})}),c.jsx(jt,{className:"p-3",children:c.jsx(Vt,{className:"p-0",children:c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:c.jsx(eS,{className:"h-4 w-4 text-green-600"})}),c.jsxs("div",{children:[c.jsx("p",{className:"text-xs text-muted-foreground",children:"Offers"}),c.jsx("p",{className:"text-lg font-semibold text-green-600",children:i.status_counts.Offer||0})]})]})})}),c.jsx(jt,{className:"p-3",children:c.jsx(Vt,{className:"p-0",children:c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:c.jsx(dS,{className:"h-4 w-4 text-yellow-600"})}),c.jsxs("div",{children:[c.jsx("p",{className:"text-xs text-muted-foreground",children:"Interviewing"}),c.jsx("p",{className:"text-lg font-semibold text-yellow-600",children:i.status_counts.Interviewing||0})]})]})})})]})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3",children:[c.jsxs("div",{className:"flex bg-muted rounded-lg p-1",children:[c.jsxs(Ee,{variant:r==="table"?"default":"ghost",size:"sm",onClick:()=>o("table"),className:"flex-1 sm:flex-none",children:[c.jsx(uS,{className:"h-4 w-4 mr-2"}),c.jsx("span",{className:"hidden sm:inline",children:"Table"})]}),c.jsxs(Ee,{variant:r==="cards"?"default":"ghost",size:"sm",onClick:()=>o("cards"),className:"flex-1 sm:flex-none",children:[c.jsx(lS,{className:"h-4 w-4 mr-2"}),c.jsx("span",{className:"hidden sm:inline",children:"Cards"})]})]}),c.jsxs(Ee,{variant:"outline",onClick:t,disabled:n,className:"flex-1 sm:flex-none",children:[c.jsx(rS,{className:"h-4 w-4 mr-2"}),n?"Exporting...":"Export CSV"]}),c.jsxs(Ee,{onClick:e,disabled:n,className:"flex-1 sm:flex-none",children:[c.jsx(Lh,{className:"h-4 w-4 mr-2"}),"New Application"]})]})]})})})},pS=Di("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-100/80",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-100/80"}},defaultVariants:{variant:"default"}});function Ye({className:e,variant:t,...n}){return c.jsx("div",{className:K(pS({variant:t}),e),...n})}const Fh=g.forwardRef(({className:e,...t},n)=>c.jsx("div",{className:"relative w-full overflow-auto",children:c.jsx("table",{ref:n,className:K("w-full caption-bottom text-sm",e),...t})}));Fh.displayName="Table";const zh=g.forwardRef(({className:e,...t},n)=>c.jsx("thead",{ref:n,className:K("[&_tr]:border-b",e),...t}));zh.displayName="TableHeader";const $h=g.forwardRef(({className:e,...t},n)=>c.jsx("tbody",{ref:n,className:K("[&_tr:last-child]:border-0",e),...t}));$h.displayName="TableBody";const mS=g.forwardRef(({className:e,...t},n)=>c.jsx("tfoot",{ref:n,className:K("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t}));mS.displayName="TableFooter";const Wa=g.forwardRef(({className:e,...t},n)=>c.jsx("tr",{ref:n,className:K("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));Wa.displayName="TableRow";const bn=g.forwardRef(({className:e,...t},n)=>c.jsx("th",{ref:n,className:K("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));bn.displayName="TableHead";const _n=g.forwardRef(({className:e,...t},n)=>c.jsx("td",{ref:n,className:K("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));_n.displayName="TableCell";const hS=g.forwardRef(({className:e,...t},n)=>c.jsx("caption",{ref:n,className:K("mt-4 text-sm text-muted-foreground",e),...t}));hS.displayName="TableCaption";const gS=({job:e,onEdit:t,onDelete:n})=>{const r=o=>{switch(o){case"Applied":return"info";case"Interviewing":return"warning";case"Offer":return"success";case"Rejected":return"destructive";case"Withdrawn":return"secondary";default:return"secondary"}};return c.jsxs(jt,{className:"card-hover fade-in group",children:[c.jsx(nh,{className:"pb-3",children:c.jsxs("div",{className:"flex justify-between items-start gap-3",children:[c.jsxs("div",{className:"flex-1 min-w-0",children:[c.jsx("h3",{className:"text-lg font-semibold text-foreground line-clamp-1 group-hover:text-primary transition-colors",children:e.position}),c.jsxs("div",{className:"flex items-center text-muted-foreground mt-1",children:[c.jsx(rc,{className:"h-4 w-4 mr-1 flex-shrink-0"}),c.jsx("span",{className:"line-clamp-1",children:e.company})]}),e.location&&c.jsxs("div",{className:"flex items-center text-sm text-muted-foreground mt-1",children:[c.jsx(Ah,{className:"h-3 w-3 mr-1 flex-shrink-0"}),c.jsx("span",{className:"line-clamp-1",children:e.location})]})]}),c.jsx(Ye,{variant:r(e.status),className:"flex-shrink-0",children:e.status})]})}),c.jsxs(Vt,{className:"py-3",children:[e.tags&&e.tags.length>0&&c.jsxs("div",{className:"flex flex-wrap gap-1 mb-4",children:[e.tags.slice(0,3).map(o=>c.jsx(Ye,{variant:"outline",className:"text-xs",style:{backgroundColor:`${o.color}10`,color:o.color,borderColor:`${o.color}30`},children:o.name},o.id)),e.tags.length>3&&c.jsxs(Ye,{variant:"outline",className:"text-xs",children:["+",e.tags.length-3," more"]})]}),c.jsxs("div",{className:"space-y-2",children:[e.salary_range&&c.jsxs("div",{className:"flex items-center text-sm",children:[c.jsx(_h,{className:"h-4 w-4 mr-2 text-muted-foreground flex-shrink-0"}),c.jsx("span",{className:"text-foreground",children:e.salary_range})]}),e.application_date&&c.jsxs("div",{className:"flex items-center text-sm",children:[c.jsx(oc,{className:"h-4 w-4 mr-2 text-muted-foreground flex-shrink-0"}),c.jsxs("span",{className:"text-muted-foreground",children:["Applied ",Ia(e.application_date)]})]}),e.contact_person&&c.jsxs("div",{className:"flex items-center text-sm",children:[c.jsx(Ih,{className:"h-4 w-4 mr-2 text-muted-foreground flex-shrink-0"}),c.jsx("span",{className:"text-foreground line-clamp-1",children:e.contact_person})]})]}),e.notes&&c.jsx("div",{className:"mt-4 p-3 bg-muted/50 rounded-md",children:c.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:v0(e.notes,100)})})]}),c.jsxs(rh,{className:"pt-3 border-t border-border",children:[c.jsxs("div",{className:"flex justify-between items-center w-full",children:[c.jsxs("div",{className:"flex space-x-2",children:[e.job_url&&c.jsx(Ee,{variant:"ghost",size:"sm",asChild:!0,className:"text-xs",children:c.jsxs("a",{href:e.job_url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center",children:[c.jsx(sc,{className:"h-3 w-3 mr-1"}),"View Job"]})}),e.contact_email&&c.jsx(Ee,{variant:"ghost",size:"sm",asChild:!0,className:"text-xs",children:c.jsxs("a",{href:`mailto:${e.contact_email}`,className:"flex items-center",children:[c.jsx(ic,{className:"h-3 w-3 mr-1"}),"Email"]})})]}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsxs(Ee,{variant:"ghost",size:"sm",onClick:()=>t(e),className:"text-xs",children:[c.jsx(Oh,{className:"h-3 w-3 mr-1"}),"Edit"]}),c.jsxs(Ee,{variant:"ghost",size:"sm",onClick:()=>n(e.id),className:"text-xs text-destructive hover:text-destructive",children:[c.jsx(Mh,{className:"h-3 w-3 mr-1"}),"Delete"]})]})]}),c.jsx("div",{className:"w-full mt-2 pt-2 border-t border-border",children:c.jsxs("p",{className:"text-xs text-muted-foreground",children:["Last updated: ",Ia(e.last_updated)]})})]})]})},vS=({jobs:e,onEdit:t,onDelete:n,loading:r})=>{const{view:o}=el(),s=i=>{switch(i){case"Applied":return"info";case"Interviewing":return"warning";case"Offer":return"success";case"Rejected":return"destructive";case"Withdrawn":return"secondary";default:return"secondary"}};return r?c.jsx(jt,{children:c.jsx(Vt,{className:"flex justify-center items-center py-12",children:c.jsxs("div",{className:"flex items-center space-x-2 text-muted-foreground",children:[c.jsx(jh,{className:"h-5 w-5 animate-spin"}),c.jsx("span",{children:"Loading job applications..."})]})})}):e.length===0?c.jsx(jt,{children:c.jsx(Vt,{className:"text-center py-12",children:c.jsxs("div",{className:"flex flex-col items-center space-y-4 text-muted-foreground",children:[c.jsx(sS,{className:"h-12 w-12"}),c.jsxs("div",{children:[c.jsx("p",{className:"text-lg font-medium",children:"No job applications found"}),c.jsx("p",{className:"text-sm mt-1",children:"Create your first application to get started"})]})]})})}):o==="cards"?c.jsx("div",{className:"grid-responsive",children:e.map(i=>c.jsx(gS,{job:i,onEdit:t,onDelete:n},i.id))}):c.jsx(jt,{children:c.jsx("div",{className:"overflow-x-auto",children:c.jsxs(Fh,{children:[c.jsx(zh,{children:c.jsxs(Wa,{children:[c.jsx(bn,{className:"min-w-[200px]",children:"Company & Position"}),c.jsx(bn,{className:"min-w-[120px]",children:"Status"}),c.jsx(bn,{className:"min-w-[100px]",children:"Applied"}),c.jsx(bn,{className:"min-w-[150px]",children:"Tags"}),c.jsx(bn,{className:"min-w-[120px]",children:"Salary"}),c.jsx(bn,{className:"min-w-[100px] text-right",children:"Actions"})]})}),c.jsx($h,{children:e.map(i=>c.jsxs(Wa,{className:"group",children:[c.jsx(_n,{children:c.jsxs("div",{className:"space-y-1",children:[c.jsx("div",{className:"font-medium text-foreground group-hover:text-primary transition-colors",children:i.position}),c.jsx("div",{className:"text-sm text-muted-foreground",children:i.company}),i.location&&c.jsx("div",{className:"text-xs text-muted-foreground",children:i.location})]})}),c.jsx(_n,{children:c.jsx(Ye,{variant:s(i.status),children:i.status})}),c.jsx(_n,{className:"text-sm text-muted-foreground",children:Ia(i.application_date)}),c.jsx(_n,{children:c.jsxs("div",{className:"flex flex-wrap gap-1",children:[i.tags&&i.tags.slice(0,2).map(l=>c.jsx(Ye,{variant:"outline",className:"text-xs",style:{backgroundColor:`${l.color}10`,color:l.color,borderColor:`${l.color}30`},children:l.name},l.id)),i.tags&&i.tags.length>2&&c.jsxs(Ye,{variant:"outline",className:"text-xs",children:["+",i.tags.length-2]})]})}),c.jsx(_n,{className:"text-sm",children:i.salary_range||"-"}),c.jsx(_n,{children:c.jsxs("div",{className:"flex justify-end space-x-1",children:[i.job_url&&c.jsx(Ee,{variant:"ghost",size:"sm",asChild:!0,className:"h-8 w-8 p-0",children:c.jsx("a",{href:i.job_url,target:"_blank",rel:"noopener noreferrer",title:"View Job",children:c.jsx(sc,{className:"h-4 w-4"})})}),i.contact_email&&c.jsx(Ee,{variant:"ghost",size:"sm",asChild:!0,className:"h-8 w-8 p-0",children:c.jsx("a",{href:`mailto:${i.contact_email}`,title:"Email Contact",children:c.jsx(ic,{className:"h-4 w-4"})})}),c.jsx(Ee,{variant:"ghost",size:"sm",onClick:()=>t(i),className:"h-8 w-8 p-0",title:"Edit",children:c.jsx(Oh,{className:"h-4 w-4"})}),c.jsx(Ee,{variant:"ghost",size:"sm",onClick:()=>n(i.id),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",title:"Delete",children:c.jsx(Mh,{className:"h-4 w-4"})})]})})]},i.id))})]})})})},Ie=g.forwardRef(({className:e,type:t,...n},r)=>c.jsx("input",{type:t,className:K("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Ie.displayName="Input";var yS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],q=yS.reduce((e,t)=>{const n=jr(`Primitive.${t}`),r=g.forwardRef((o,s)=>{const{asChild:i,...l}=o,a=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),c.jsx(a,{...l,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Uh(e,t){e&&qn.flushSync(()=>e.dispatchEvent(t))}var xS="Label",Bh=g.forwardRef((e,t)=>c.jsx(q.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Bh.displayName=xS;var Vh=Bh;const wS=Di("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),we=g.forwardRef(({className:e,...t},n)=>c.jsx(Vh,{ref:n,className:K(wS(),e),...t}));we.displayName=Vh.displayName;function df(e,[t,n]){return Math.min(n,Math.max(t,e))}function G(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function SS(e,t){const n=g.createContext(t),r=s=>{const{children:i,...l}=s,a=g.useMemo(()=>l,Object.values(l));return c.jsx(n.Provider,{value:a,children:i})};r.displayName=e+"Provider";function o(s){const i=g.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function Ho(e,t=[]){let n=[];function r(s,i){const l=g.createContext(i),a=n.length;n=[...n,i];const u=m=>{var h;const{scope:y,children:w,...S}=m,p=((h=y==null?void 0:y[e])==null?void 0:h[a])||l,x=g.useMemo(()=>S,Object.values(S));return c.jsx(p.Provider,{value:x,children:w})};u.displayName=s+"Provider";function f(m,y){var p;const w=((p=y==null?void 0:y[e])==null?void 0:p[a])||l,S=g.useContext(w);if(S)return S;if(i!==void 0)return i;throw new Error(`\`${m}\` must be used within \`${s}\``)}return[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(l){const a=(l==null?void 0:l[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,ES(o,...t)]}function ES(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:a,scopeName:u})=>{const m=a(s)[`__scope${u}`];return{...l,...m}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Wh(e){const t=e+"CollectionProvider",[n,r]=Ho(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=p=>{const{scope:x,children:h}=p,d=Ft.useRef(null),v=Ft.useRef(new Map).current;return c.jsx(o,{scope:x,itemMap:v,collectionRef:d,children:h})};i.displayName=t;const l=e+"CollectionSlot",a=jr(l),u=Ft.forwardRef((p,x)=>{const{scope:h,children:d}=p,v=s(l,h),E=fe(x,v.collectionRef);return c.jsx(a,{ref:E,children:d})});u.displayName=l;const f=e+"CollectionItemSlot",m="data-radix-collection-item",y=jr(f),w=Ft.forwardRef((p,x)=>{const{scope:h,children:d,...v}=p,E=Ft.useRef(null),C=fe(x,E),T=s(f,h);return Ft.useEffect(()=>(T.itemMap.set(E,{ref:E,...v}),()=>void T.itemMap.delete(E))),c.jsx(y,{[m]:"",ref:C,children:d})});w.displayName=f;function S(p){const x=s(e+"CollectionConsumer",p);return Ft.useCallback(()=>{const d=x.collectionRef.current;if(!d)return[];const v=Array.from(d.querySelectorAll(`[${m}]`));return Array.from(x.itemMap.values()).sort((T,N)=>v.indexOf(T.ref.current)-v.indexOf(N.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:u,ItemSlot:w},S,r]}var CS=g.createContext(void 0);function NS(e){const t=g.useContext(CS);return e||t||"ltr"}function xt(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function kS(e,t=globalThis==null?void 0:globalThis.document){const n=xt(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var TS="DismissableLayer",Ha="dismissableLayer.update",RS="dismissableLayer.pointerDownOutside",PS="dismissableLayer.focusOutside",ff,Hh=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Vi=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:l,...a}=e,u=g.useContext(Hh),[f,m]=g.useState(null),y=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,w]=g.useState({}),S=fe(t,N=>m(N)),p=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=p.indexOf(x),d=f?p.indexOf(f):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,E=d>=h,C=_S(N=>{const R=N.target,j=[...u.branches].some(_=>_.contains(R));!E||j||(o==null||o(N),i==null||i(N),N.defaultPrevented||l==null||l())},y),T=jS(N=>{const R=N.target;[...u.branches].some(_=>_.contains(R))||(s==null||s(N),i==null||i(N),N.defaultPrevented||l==null||l())},y);return kS(N=>{d===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&l&&(N.preventDefault(),l()))},y),g.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(ff=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),pf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(y.body.style.pointerEvents=ff)}},[f,y,n,u]),g.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),pf())},[f,u]),g.useEffect(()=>{const N=()=>w({});return document.addEventListener(Ha,N),()=>document.removeEventListener(Ha,N)},[]),c.jsx(q.div,{...a,ref:S,style:{pointerEvents:v?E?"auto":"none":void 0,...e.style},onFocusCapture:G(e.onFocusCapture,T.onFocusCapture),onBlurCapture:G(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:G(e.onPointerDownCapture,C.onPointerDownCapture)})});Vi.displayName=TS;var bS="DismissableLayerBranch",Kh=g.forwardRef((e,t)=>{const n=g.useContext(Hh),r=g.useRef(null),o=fe(t,r);return g.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),c.jsx(q.div,{...e,ref:o})});Kh.displayName=bS;function _S(e,t=globalThis==null?void 0:globalThis.document){const n=xt(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const s=l=>{if(l.target&&!r.current){let a=function(){Qh(RS,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function jS(e,t=globalThis==null?void 0:globalThis.document){const n=xt(e),r=g.useRef(!1);return g.useEffect(()=>{const o=s=>{s.target&&!r.current&&Qh(PS,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function pf(){const e=new CustomEvent(Ha);document.dispatchEvent(e)}function Qh(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Uh(o,s):o.dispatchEvent(s)}var AS=Vi,OS=Kh,jl=0;function Gh(){g.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??mf()),document.body.insertAdjacentElement("beforeend",e[1]??mf()),jl++,()=>{jl===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),jl--}},[])}function mf(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Al="focusScope.autoFocusOnMount",Ol="focusScope.autoFocusOnUnmount",hf={bubbles:!1,cancelable:!0},LS="FocusScope",ac=g.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[l,a]=g.useState(null),u=xt(o),f=xt(s),m=g.useRef(null),y=fe(t,p=>a(p)),w=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(r){let p=function(v){if(w.paused||!l)return;const E=v.target;l.contains(E)?m.current=E:nn(m.current,{select:!0})},x=function(v){if(w.paused||!l)return;const E=v.relatedTarget;E!==null&&(l.contains(E)||nn(m.current,{select:!0}))},h=function(v){if(document.activeElement===document.body)for(const C of v)C.removedNodes.length>0&&nn(l)};document.addEventListener("focusin",p),document.addEventListener("focusout",x);const d=new MutationObserver(h);return l&&d.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",x),d.disconnect()}}},[r,l,w.paused]),g.useEffect(()=>{if(l){vf.add(w);const p=document.activeElement;if(!l.contains(p)){const h=new CustomEvent(Al,hf);l.addEventListener(Al,u),l.dispatchEvent(h),h.defaultPrevented||(DS($S(Xh(l)),{select:!0}),document.activeElement===p&&nn(l))}return()=>{l.removeEventListener(Al,u),setTimeout(()=>{const h=new CustomEvent(Ol,hf);l.addEventListener(Ol,f),l.dispatchEvent(h),h.defaultPrevented||nn(p??document.body,{select:!0}),l.removeEventListener(Ol,f),vf.remove(w)},0)}}},[l,u,f,w]);const S=g.useCallback(p=>{if(!n&&!r||w.paused)return;const x=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,h=document.activeElement;if(x&&h){const d=p.currentTarget,[v,E]=MS(d);v&&E?!p.shiftKey&&h===E?(p.preventDefault(),n&&nn(v,{select:!0})):p.shiftKey&&h===v&&(p.preventDefault(),n&&nn(E,{select:!0})):h===d&&p.preventDefault()}},[n,r,w.paused]);return c.jsx(q.div,{tabIndex:-1,...i,ref:y,onKeyDown:S})});ac.displayName=LS;function DS(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(nn(r,{select:t}),document.activeElement!==n)return}function MS(e){const t=Xh(e),n=gf(t,e),r=gf(t.reverse(),e);return[n,r]}function Xh(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function gf(e,t){for(const n of e)if(!IS(n,{upTo:t}))return n}function IS(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function FS(e){return e instanceof HTMLInputElement&&"select"in e}function nn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&FS(e)&&t&&e.select()}}var vf=zS();function zS(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=yf(e,t),e.unshift(t)},remove(t){var n;e=yf(e,t),(n=e[0])==null||n.resume()}}}function yf(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function $S(e){return e.filter(t=>t.tagName!=="A")}var Re=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},US=Hf[" useId ".trim().toString()]||(()=>{}),BS=0;function Sr(e){const[t,n]=g.useState(US());return Re(()=>{n(r=>r??String(BS++))},[e]),t?`radix-${t}`:""}const VS=["top","right","bottom","left"],wn=Math.min,Je=Math.max,fi=Math.round,ps=Math.floor,Lt=e=>({x:e,y:e}),WS={left:"right",right:"left",bottom:"top",top:"bottom"},HS={start:"end",end:"start"};function Ka(e,t,n){return Je(e,wn(t,n))}function qt(e,t){return typeof e=="function"?e(t):e}function Yt(e){return e.split("-")[0]}function zr(e){return e.split("-")[1]}function uc(e){return e==="x"?"y":"x"}function cc(e){return e==="y"?"height":"width"}function Ht(e){return["top","bottom"].includes(Yt(e))?"y":"x"}function dc(e){return uc(Ht(e))}function KS(e,t,n){n===void 0&&(n=!1);const r=zr(e),o=dc(e),s=cc(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=pi(i)),[i,pi(i)]}function QS(e){const t=pi(e);return[Qa(e),t,Qa(t)]}function Qa(e){return e.replace(/start|end/g,t=>HS[t])}function GS(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function XS(e,t,n,r){const o=zr(e);let s=GS(Yt(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Qa)))),s}function pi(e){return e.replace(/left|right|bottom|top/g,t=>WS[t])}function qS(e){return{top:0,right:0,bottom:0,left:0,...e}}function qh(e){return typeof e!="number"?qS(e):{top:e,right:e,bottom:e,left:e}}function mi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function xf(e,t,n){let{reference:r,floating:o}=e;const s=Ht(t),i=dc(t),l=cc(i),a=Yt(t),u=s==="y",f=r.x+r.width/2-o.width/2,m=r.y+r.height/2-o.height/2,y=r[l]/2-o[l]/2;let w;switch(a){case"top":w={x:f,y:r.y-o.height};break;case"bottom":w={x:f,y:r.y+r.height};break;case"right":w={x:r.x+r.width,y:m};break;case"left":w={x:r.x-o.width,y:m};break;default:w={x:r.x,y:r.y}}switch(zr(t)){case"start":w[i]-=y*(n&&u?-1:1);break;case"end":w[i]+=y*(n&&u?-1:1);break}return w}const YS=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,l=s.filter(Boolean),a=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:m}=xf(u,r,a),y=r,w={},S=0;for(let p=0;p<l.length;p++){const{name:x,fn:h}=l[p],{x:d,y:v,data:E,reset:C}=await h({x:f,y:m,initialPlacement:r,placement:y,strategy:o,middlewareData:w,rects:u,platform:i,elements:{reference:e,floating:t}});f=d??f,m=v??m,w={...w,[x]:{...w[x],...E}},C&&S<=50&&(S++,typeof C=="object"&&(C.placement&&(y=C.placement),C.rects&&(u=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:f,y:m}=xf(u,y,a)),p=-1)}return{x:f,y:m,placement:y,strategy:o,middlewareData:w}};async function Mo(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:m="floating",altBoundary:y=!1,padding:w=0}=qt(t,e),S=qh(w),x=l[y?m==="floating"?"reference":"floating":m],h=mi(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(x)))==null||n?x:x.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:a})),d=m==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,v=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),E=await(s.isElement==null?void 0:s.isElement(v))?await(s.getScale==null?void 0:s.getScale(v))||{x:1,y:1}:{x:1,y:1},C=mi(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:d,offsetParent:v,strategy:a}):d);return{top:(h.top-C.top+S.top)/E.y,bottom:(C.bottom-h.bottom+S.bottom)/E.y,left:(h.left-C.left+S.left)/E.x,right:(C.right-h.right+S.right)/E.x}}const JS=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:l,middlewareData:a}=t,{element:u,padding:f=0}=qt(e,t)||{};if(u==null)return{};const m=qh(f),y={x:n,y:r},w=dc(o),S=cc(w),p=await i.getDimensions(u),x=w==="y",h=x?"top":"left",d=x?"bottom":"right",v=x?"clientHeight":"clientWidth",E=s.reference[S]+s.reference[w]-y[w]-s.floating[S],C=y[w]-s.reference[w],T=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let N=T?T[v]:0;(!N||!await(i.isElement==null?void 0:i.isElement(T)))&&(N=l.floating[v]||s.floating[S]);const R=E/2-C/2,j=N/2-p[S]/2-1,_=wn(m[h],j),I=wn(m[d],j),L=_,B=N-p[S]-I,O=N/2-p[S]/2+R,$=Ka(L,O,B),F=!a.arrow&&zr(o)!=null&&O!==$&&s.reference[S]/2-(O<L?_:I)-p[S]/2<0,W=F?O<L?O-L:O-B:0;return{[w]:y[w]+W,data:{[w]:$,centerOffset:O-$-W,...F&&{alignmentOffset:W}},reset:F}}}),ZS=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:f=!0,crossAxis:m=!0,fallbackPlacements:y,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:p=!0,...x}=qt(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const h=Yt(o),d=Ht(l),v=Yt(l)===l,E=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=y||(v||!p?[pi(l)]:QS(l)),T=S!=="none";!y&&T&&C.push(...XS(l,p,S,E));const N=[l,...C],R=await Mo(t,x),j=[];let _=((r=s.flip)==null?void 0:r.overflows)||[];if(f&&j.push(R[h]),m){const $=KS(o,i,E);j.push(R[$[0]],R[$[1]])}if(_=[..._,{placement:o,overflows:j}],!j.every($=>$<=0)){var I,L;const $=(((I=s.flip)==null?void 0:I.index)||0)+1,F=N[$];if(F){var B;const P=m==="alignment"?d!==Ht(F):!1,A=((B=_[0])==null?void 0:B.overflows[0])>0;if(!P||A)return{data:{index:$,overflows:_},reset:{placement:F}}}let W=(L=_.filter(P=>P.overflows[0]<=0).sort((P,A)=>P.overflows[1]-A.overflows[1])[0])==null?void 0:L.placement;if(!W)switch(w){case"bestFit":{var O;const P=(O=_.filter(A=>{if(T){const M=Ht(A.placement);return M===d||M==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(M=>M>0).reduce((M,U)=>M+U,0)]).sort((A,M)=>A[1]-M[1])[0])==null?void 0:O[0];P&&(W=P);break}case"initialPlacement":W=l;break}if(o!==W)return{reset:{placement:W}}}return{}}}};function wf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Sf(e){return VS.some(t=>e[t]>=0)}const eE=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=qt(e,t);switch(r){case"referenceHidden":{const s=await Mo(t,{...o,elementContext:"reference"}),i=wf(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Sf(i)}}}case"escaped":{const s=await Mo(t,{...o,altBoundary:!0}),i=wf(s,n.floating);return{data:{escapedOffsets:i,escaped:Sf(i)}}}default:return{}}}}};async function tE(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=Yt(n),l=zr(n),a=Ht(n)==="y",u=["left","top"].includes(i)?-1:1,f=s&&a?-1:1,m=qt(t,e);let{mainAxis:y,crossAxis:w,alignmentAxis:S}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return l&&typeof S=="number"&&(w=l==="end"?S*-1:S),a?{x:w*f,y:y*u}:{x:y*u,y:w*f}}const nE=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:l}=t,a=await tE(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:s+a.y,data:{...a,placement:i}}}}},rE=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:l={fn:x=>{let{x:h,y:d}=x;return{x:h,y:d}}},...a}=qt(e,t),u={x:n,y:r},f=await Mo(t,a),m=Ht(Yt(o)),y=uc(m);let w=u[y],S=u[m];if(s){const x=y==="y"?"top":"left",h=y==="y"?"bottom":"right",d=w+f[x],v=w-f[h];w=Ka(d,w,v)}if(i){const x=m==="y"?"top":"left",h=m==="y"?"bottom":"right",d=S+f[x],v=S-f[h];S=Ka(d,S,v)}const p=l.fn({...t,[y]:w,[m]:S});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[y]:s,[m]:i}}}}}},oE=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=qt(e,t),f={x:n,y:r},m=Ht(o),y=uc(m);let w=f[y],S=f[m];const p=qt(l,t),x=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(a){const v=y==="y"?"height":"width",E=s.reference[y]-s.floating[v]+x.mainAxis,C=s.reference[y]+s.reference[v]-x.mainAxis;w<E?w=E:w>C&&(w=C)}if(u){var h,d;const v=y==="y"?"width":"height",E=["top","left"].includes(Yt(o)),C=s.reference[m]-s.floating[v]+(E&&((h=i.offset)==null?void 0:h[m])||0)+(E?0:x.crossAxis),T=s.reference[m]+s.reference[v]+(E?0:((d=i.offset)==null?void 0:d[m])||0)-(E?x.crossAxis:0);S<C?S=C:S>T&&(S=T)}return{[y]:w,[m]:S}}}},sE=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:l}=t,{apply:a=()=>{},...u}=qt(e,t),f=await Mo(t,u),m=Yt(o),y=zr(o),w=Ht(o)==="y",{width:S,height:p}=s.floating;let x,h;m==="top"||m==="bottom"?(x=m,h=y===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(h=m,x=y==="end"?"top":"bottom");const d=p-f.top-f.bottom,v=S-f.left-f.right,E=wn(p-f[x],d),C=wn(S-f[h],v),T=!t.middlewareData.shift;let N=E,R=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=d),T&&!y){const _=Je(f.left,0),I=Je(f.right,0),L=Je(f.top,0),B=Je(f.bottom,0);w?R=S-2*(_!==0||I!==0?_+I:Je(f.left,f.right)):N=p-2*(L!==0||B!==0?L+B:Je(f.top,f.bottom))}await a({...t,availableWidth:R,availableHeight:N});const j=await i.getDimensions(l.floating);return S!==j.width||p!==j.height?{reset:{rects:!0}}:{}}}};function Wi(){return typeof window<"u"}function $r(e){return Yh(e)?(e.nodeName||"").toLowerCase():"#document"}function tt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Mt(e){var t;return(t=(Yh(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Yh(e){return Wi()?e instanceof Node||e instanceof tt(e).Node:!1}function wt(e){return Wi()?e instanceof Element||e instanceof tt(e).Element:!1}function Dt(e){return Wi()?e instanceof HTMLElement||e instanceof tt(e).HTMLElement:!1}function Ef(e){return!Wi()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof tt(e).ShadowRoot}function Ko(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=St(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function iE(e){return["table","td","th"].includes($r(e))}function Hi(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function fc(e){const t=pc(),n=wt(e)?St(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function lE(e){let t=Sn(e);for(;Dt(t)&&!Ar(t);){if(fc(t))return t;if(Hi(t))return null;t=Sn(t)}return null}function pc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ar(e){return["html","body","#document"].includes($r(e))}function St(e){return tt(e).getComputedStyle(e)}function Ki(e){return wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Sn(e){if($r(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ef(e)&&e.host||Mt(e);return Ef(t)?t.host:t}function Jh(e){const t=Sn(e);return Ar(t)?e.ownerDocument?e.ownerDocument.body:e.body:Dt(t)&&Ko(t)?t:Jh(t)}function Io(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Jh(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=tt(o);if(s){const l=Ga(i);return t.concat(i,i.visualViewport||[],Ko(o)?o:[],l&&n?Io(l):[])}return t.concat(o,Io(o,[],n))}function Ga(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Zh(e){const t=St(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Dt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,l=fi(n)!==s||fi(r)!==i;return l&&(n=s,r=i),{width:n,height:r,$:l}}function mc(e){return wt(e)?e:e.contextElement}function Er(e){const t=mc(e);if(!Dt(t))return Lt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Zh(t);let i=(s?fi(n.width):n.width)/r,l=(s?fi(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const aE=Lt(0);function eg(e){const t=tt(e);return!pc()||!t.visualViewport?aE:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function uE(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==tt(e)?!1:t}function Wn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=mc(e);let i=Lt(1);t&&(r?wt(r)&&(i=Er(r)):i=Er(e));const l=uE(s,n,r)?eg(s):Lt(0);let a=(o.left+l.x)/i.x,u=(o.top+l.y)/i.y,f=o.width/i.x,m=o.height/i.y;if(s){const y=tt(s),w=r&&wt(r)?tt(r):r;let S=y,p=Ga(S);for(;p&&r&&w!==S;){const x=Er(p),h=p.getBoundingClientRect(),d=St(p),v=h.left+(p.clientLeft+parseFloat(d.paddingLeft))*x.x,E=h.top+(p.clientTop+parseFloat(d.paddingTop))*x.y;a*=x.x,u*=x.y,f*=x.x,m*=x.y,a+=v,u+=E,S=tt(p),p=Ga(S)}}return mi({width:f,height:m,x:a,y:u})}function hc(e,t){const n=Ki(e).scrollLeft;return t?t.left+n:Wn(Mt(e)).left+n}function tg(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:hc(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function cE(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Mt(r),l=t?Hi(t.floating):!1;if(r===i||l&&s)return n;let a={scrollLeft:0,scrollTop:0},u=Lt(1);const f=Lt(0),m=Dt(r);if((m||!m&&!s)&&(($r(r)!=="body"||Ko(i))&&(a=Ki(r)),Dt(r))){const w=Wn(r);u=Er(r),f.x=w.x+r.clientLeft,f.y=w.y+r.clientTop}const y=i&&!m&&!s?tg(i,a,!0):Lt(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+f.x+y.x,y:n.y*u.y-a.scrollTop*u.y+f.y+y.y}}function dE(e){return Array.from(e.getClientRects())}function fE(e){const t=Mt(e),n=Ki(e),r=e.ownerDocument.body,o=Je(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=Je(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+hc(e);const l=-n.scrollTop;return St(r).direction==="rtl"&&(i+=Je(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:l}}function pE(e,t){const n=tt(e),r=Mt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,l=0,a=0;if(o){s=o.width,i=o.height;const u=pc();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:s,height:i,x:l,y:a}}function mE(e,t){const n=Wn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Dt(e)?Er(e):Lt(1),i=e.clientWidth*s.x,l=e.clientHeight*s.y,a=o*s.x,u=r*s.y;return{width:i,height:l,x:a,y:u}}function Cf(e,t,n){let r;if(t==="viewport")r=pE(e,n);else if(t==="document")r=fE(Mt(e));else if(wt(t))r=mE(t,n);else{const o=eg(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return mi(r)}function ng(e,t){const n=Sn(e);return n===t||!wt(n)||Ar(n)?!1:St(n).position==="fixed"||ng(n,t)}function hE(e,t){const n=t.get(e);if(n)return n;let r=Io(e,[],!1).filter(l=>wt(l)&&$r(l)!=="body"),o=null;const s=St(e).position==="fixed";let i=s?Sn(e):e;for(;wt(i)&&!Ar(i);){const l=St(i),a=fc(i);!a&&l.position==="fixed"&&(o=null),(s?!a&&!o:!a&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ko(i)&&!a&&ng(e,i))?r=r.filter(f=>f!==i):o=l,i=Sn(i)}return t.set(e,r),r}function gE(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Hi(t)?[]:hE(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((u,f)=>{const m=Cf(t,f,o);return u.top=Je(m.top,u.top),u.right=wn(m.right,u.right),u.bottom=wn(m.bottom,u.bottom),u.left=Je(m.left,u.left),u},Cf(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function vE(e){const{width:t,height:n}=Zh(e);return{width:t,height:n}}function yE(e,t,n){const r=Dt(t),o=Mt(t),s=n==="fixed",i=Wn(e,!0,s,t);let l={scrollLeft:0,scrollTop:0};const a=Lt(0);function u(){a.x=hc(o)}if(r||!r&&!s)if(($r(t)!=="body"||Ko(o))&&(l=Ki(t)),r){const w=Wn(t,!0,s,t);a.x=w.x+t.clientLeft,a.y=w.y+t.clientTop}else o&&u();s&&!r&&o&&u();const f=o&&!r&&!s?tg(o,l):Lt(0),m=i.left+l.scrollLeft-a.x-f.x,y=i.top+l.scrollTop-a.y-f.y;return{x:m,y,width:i.width,height:i.height}}function Ll(e){return St(e).position==="static"}function Nf(e,t){if(!Dt(e)||St(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Mt(e)===n&&(n=n.ownerDocument.body),n}function rg(e,t){const n=tt(e);if(Hi(e))return n;if(!Dt(e)){let o=Sn(e);for(;o&&!Ar(o);){if(wt(o)&&!Ll(o))return o;o=Sn(o)}return n}let r=Nf(e,t);for(;r&&iE(r)&&Ll(r);)r=Nf(r,t);return r&&Ar(r)&&Ll(r)&&!fc(r)?n:r||lE(e)||n}const xE=async function(e){const t=this.getOffsetParent||rg,n=this.getDimensions,r=await n(e.floating);return{reference:yE(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function wE(e){return St(e).direction==="rtl"}const SE={convertOffsetParentRelativeRectToViewportRelativeRect:cE,getDocumentElement:Mt,getClippingRect:gE,getOffsetParent:rg,getElementRects:xE,getClientRects:dE,getDimensions:vE,getScale:Er,isElement:wt,isRTL:wE};function og(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function EE(e,t){let n=null,r;const o=Mt(e);function s(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function i(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),s();const u=e.getBoundingClientRect(),{left:f,top:m,width:y,height:w}=u;if(l||t(),!y||!w)return;const S=ps(m),p=ps(o.clientWidth-(f+y)),x=ps(o.clientHeight-(m+w)),h=ps(f),v={rootMargin:-S+"px "+-p+"px "+-x+"px "+-h+"px",threshold:Je(0,wn(1,a))||1};let E=!0;function C(T){const N=T[0].intersectionRatio;if(N!==a){if(!E)return i();N?i(!1,N):r=setTimeout(()=>{i(!1,1e-7)},1e3)}N===1&&!og(u,e.getBoundingClientRect())&&i(),E=!1}try{n=new IntersectionObserver(C,{...v,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,v)}n.observe(e)}return i(!0),s}function CE(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=mc(e),f=o||s?[...u?Io(u):[],...Io(t)]:[];f.forEach(h=>{o&&h.addEventListener("scroll",n,{passive:!0}),s&&h.addEventListener("resize",n)});const m=u&&l?EE(u,n):null;let y=-1,w=null;i&&(w=new ResizeObserver(h=>{let[d]=h;d&&d.target===u&&w&&(w.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var v;(v=w)==null||v.observe(t)})),n()}),u&&!a&&w.observe(u),w.observe(t));let S,p=a?Wn(e):null;a&&x();function x(){const h=Wn(e);p&&!og(p,h)&&n(),p=h,S=requestAnimationFrame(x)}return n(),()=>{var h;f.forEach(d=>{o&&d.removeEventListener("scroll",n),s&&d.removeEventListener("resize",n)}),m==null||m(),(h=w)==null||h.disconnect(),w=null,a&&cancelAnimationFrame(S)}}const NE=nE,kE=rE,TE=ZS,RE=sE,PE=eE,kf=JS,bE=oE,_E=(e,t,n)=>{const r=new Map,o={platform:SE,...n},s={...o.platform,_c:r};return YS(e,t,{...o,platform:s})};var Ds=typeof document<"u"?g.useLayoutEffect:g.useEffect;function hi(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!hi(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!hi(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function sg(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Tf(e,t){const n=sg(e);return Math.round(t*n)/n}function Dl(e){const t=g.useRef(e);return Ds(()=>{t.current=e}),t}function jE(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[f,m]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,w]=g.useState(r);hi(y,r)||w(r);const[S,p]=g.useState(null),[x,h]=g.useState(null),d=g.useCallback(P=>{P!==T.current&&(T.current=P,p(P))},[]),v=g.useCallback(P=>{P!==N.current&&(N.current=P,h(P))},[]),E=s||S,C=i||x,T=g.useRef(null),N=g.useRef(null),R=g.useRef(f),j=a!=null,_=Dl(a),I=Dl(o),L=Dl(u),B=g.useCallback(()=>{if(!T.current||!N.current)return;const P={placement:t,strategy:n,middleware:y};I.current&&(P.platform=I.current),_E(T.current,N.current,P).then(A=>{const M={...A,isPositioned:L.current!==!1};O.current&&!hi(R.current,M)&&(R.current=M,qn.flushSync(()=>{m(M)}))})},[y,t,n,I,L]);Ds(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,m(P=>({...P,isPositioned:!1})))},[u]);const O=g.useRef(!1);Ds(()=>(O.current=!0,()=>{O.current=!1}),[]),Ds(()=>{if(E&&(T.current=E),C&&(N.current=C),E&&C){if(_.current)return _.current(E,C,B);B()}},[E,C,B,_,j]);const $=g.useMemo(()=>({reference:T,floating:N,setReference:d,setFloating:v}),[d,v]),F=g.useMemo(()=>({reference:E,floating:C}),[E,C]),W=g.useMemo(()=>{const P={position:n,left:0,top:0};if(!F.floating)return P;const A=Tf(F.floating,f.x),M=Tf(F.floating,f.y);return l?{...P,transform:"translate("+A+"px, "+M+"px)",...sg(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:M}},[n,l,F.floating,f.x,f.y]);return g.useMemo(()=>({...f,update:B,refs:$,elements:F,floatingStyles:W}),[f,B,$,F,W])}const AE=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?kf({element:r.current,padding:o}).fn(n):{}:r?kf({element:r,padding:o}).fn(n):{}}}},OE=(e,t)=>({...NE(e),options:[e,t]}),LE=(e,t)=>({...kE(e),options:[e,t]}),DE=(e,t)=>({...bE(e),options:[e,t]}),ME=(e,t)=>({...TE(e),options:[e,t]}),IE=(e,t)=>({...RE(e),options:[e,t]}),FE=(e,t)=>({...PE(e),options:[e,t]}),zE=(e,t)=>({...AE(e),options:[e,t]});var $E="Arrow",ig=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return c.jsx(q.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:c.jsx("polygon",{points:"0,0 30,0 15,10"})})});ig.displayName=$E;var UE=ig;function BE(e){const[t,n]=g.useState(void 0);return Re(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,l;if("borderBoxSize"in s){const a=s.borderBoxSize,u=Array.isArray(a)?a[0]:a;i=u.inlineSize,l=u.blockSize}else i=e.offsetWidth,l=e.offsetHeight;n({width:i,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var gc="Popper",[lg,ag]=Ho(gc),[VE,ug]=lg(gc),cg=e=>{const{__scopePopper:t,children:n}=e,[r,o]=g.useState(null);return c.jsx(VE,{scope:t,anchor:r,onAnchorChange:o,children:n})};cg.displayName=gc;var dg="PopperAnchor",fg=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=ug(dg,n),i=g.useRef(null),l=fe(t,i);return g.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:c.jsx(q.div,{...o,ref:l})});fg.displayName=dg;var vc="PopperContent",[WE,HE]=lg(vc),pg=g.forwardRef((e,t)=>{var z,oe,Pe,te,Z,ee;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:m="partial",hideWhenDetached:y=!1,updatePositionStrategy:w="optimized",onPlaced:S,...p}=e,x=ug(vc,n),[h,d]=g.useState(null),v=fe(t,Ge=>d(Ge)),[E,C]=g.useState(null),T=BE(E),N=(T==null?void 0:T.width)??0,R=(T==null?void 0:T.height)??0,j=r+(s!=="center"?"-"+s:""),_=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},I=Array.isArray(u)?u:[u],L=I.length>0,B={padding:_,boundary:I.filter(QE),altBoundary:L},{refs:O,floatingStyles:$,placement:F,isPositioned:W,middlewareData:P}=jE({strategy:"fixed",placement:j,whileElementsMounted:(...Ge)=>CE(...Ge,{animationFrame:w==="always"}),elements:{reference:x.anchor},middleware:[OE({mainAxis:o+R,alignmentAxis:i}),a&&LE({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?DE():void 0,...B}),a&&ME({...B}),IE({...B,apply:({elements:Ge,rects:kt,availableWidth:Br,availableHeight:Vr})=>{const{width:Wr,height:sy}=kt.reference,Xo=Ge.floating.style;Xo.setProperty("--radix-popper-available-width",`${Br}px`),Xo.setProperty("--radix-popper-available-height",`${Vr}px`),Xo.setProperty("--radix-popper-anchor-width",`${Wr}px`),Xo.setProperty("--radix-popper-anchor-height",`${sy}px`)}}),E&&zE({element:E,padding:l}),GE({arrowWidth:N,arrowHeight:R}),y&&FE({strategy:"referenceHidden",...B})]}),[A,M]=gg(F),U=xt(S);Re(()=>{W&&(U==null||U())},[W,U]);const J=(z=P.arrow)==null?void 0:z.x,Le=(oe=P.arrow)==null?void 0:oe.y,ve=((Pe=P.arrow)==null?void 0:Pe.centerOffset)!==0,[Nt,De]=g.useState();return Re(()=>{h&&De(window.getComputedStyle(h).zIndex)},[h]),c.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:W?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Nt,"--radix-popper-transform-origin":[(te=P.transformOrigin)==null?void 0:te.x,(Z=P.transformOrigin)==null?void 0:Z.y].join(" "),...((ee=P.hide)==null?void 0:ee.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:c.jsx(WE,{scope:n,placedSide:A,onArrowChange:C,arrowX:J,arrowY:Le,shouldHideArrow:ve,children:c.jsx(q.div,{"data-side":A,"data-align":M,...p,ref:v,style:{...p.style,animation:W?void 0:"none"}})})})});pg.displayName=vc;var mg="PopperArrow",KE={top:"bottom",right:"left",bottom:"top",left:"right"},hg=g.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=HE(mg,r),i=KE[s.placedSide];return c.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:c.jsx(UE,{...o,ref:n,style:{...o.style,display:"block"}})})});hg.displayName=mg;function QE(e){return e!==null}var GE=e=>({name:"transformOrigin",options:e,fn(t){var x,h,d;const{placement:n,rects:r,middlewareData:o}=t,i=((x=o.arrow)==null?void 0:x.centerOffset)!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,f]=gg(n),m={start:"0%",center:"50%",end:"100%"}[f],y=(((h=o.arrow)==null?void 0:h.x)??0)+l/2,w=(((d=o.arrow)==null?void 0:d.y)??0)+a/2;let S="",p="";return u==="bottom"?(S=i?m:`${y}px`,p=`${-a}px`):u==="top"?(S=i?m:`${y}px`,p=`${r.floating.height+a}px`):u==="right"?(S=`${-a}px`,p=i?m:`${w}px`):u==="left"&&(S=`${r.floating.width+a}px`,p=i?m:`${w}px`),{data:{x:S,y:p}}}});function gg(e){const[t,n="center"]=e.split("-");return[t,n]}var XE=cg,qE=fg,YE=pg,JE=hg,ZE="Portal",Qi=g.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,s]=g.useState(!1);Re(()=>s(!0),[]);const i=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return i?Aw.createPortal(c.jsx(q.div,{...r,ref:t}),i):null});Qi.displayName=ZE;var eC=Hf[" useInsertionEffect ".trim().toString()]||Re;function gi({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,i]=tC({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const f=g.useRef(e!==void 0);g.useEffect(()=>{const m=f.current;m!==l&&console.warn(`${r} is changing from ${m?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=l},[l,r])}const u=g.useCallback(f=>{var m;if(l){const y=nC(f)?f(e):f;y!==e&&((m=i.current)==null||m.call(i,y))}else s(f)},[l,e,s,i]);return[a,u]}function tC({defaultProp:e,onChange:t}){const[n,r]=g.useState(e),o=g.useRef(n),s=g.useRef(t);return eC(()=>{s.current=t},[t]),g.useEffect(()=>{var i;o.current!==n&&((i=s.current)==null||i.call(s,n),o.current=n)},[n,o]),[n,r,s]}function nC(e){return typeof e=="function"}function rC(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var vg=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),oC="VisuallyHidden",yc=g.forwardRef((e,t)=>c.jsx(q.span,{...e,ref:t,style:{...vg,...e.style}}));yc.displayName=oC;var sC=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Jn=new WeakMap,ms=new WeakMap,hs={},Ml=0,yg=function(e){return e&&(e.host||yg(e.parentNode))},iC=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=yg(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},lC=function(e,t,n,r){var o=iC(t,Array.isArray(e)?e:[e]);hs[n]||(hs[n]=new WeakMap);var s=hs[n],i=[],l=new Set,a=new Set(o),u=function(m){!m||l.has(m)||(l.add(m),u(m.parentNode))};o.forEach(u);var f=function(m){!m||a.has(m)||Array.prototype.forEach.call(m.children,function(y){if(l.has(y))f(y);else try{var w=y.getAttribute(r),S=w!==null&&w!=="false",p=(Jn.get(y)||0)+1,x=(s.get(y)||0)+1;Jn.set(y,p),s.set(y,x),i.push(y),p===1&&S&&ms.set(y,!0),x===1&&y.setAttribute(n,"true"),S||y.setAttribute(r,"true")}catch(h){console.error("aria-hidden: cannot operate on ",y,h)}})};return f(t),l.clear(),Ml++,function(){i.forEach(function(m){var y=Jn.get(m)-1,w=s.get(m)-1;Jn.set(m,y),s.set(m,w),y||(ms.has(m)||m.removeAttribute(r),ms.delete(m)),w||m.removeAttribute(n)}),Ml--,Ml||(Jn=new WeakMap,Jn=new WeakMap,ms=new WeakMap,hs={})}},xg=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=sC(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),lC(r,o,n,"aria-hidden")):function(){return null}},_t=function(){return _t=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},_t.apply(this,arguments)};function wg(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function aC(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Ms="right-scroll-bar-position",Is="width-before-scroll-bar",uC="with-scroll-bars-hidden",cC="--removed-body-scroll-bar-size";function Il(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function dC(e,t){var n=g.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var fC=typeof window<"u"?g.useLayoutEffect:g.useEffect,Rf=new WeakMap;function pC(e,t){var n=dC(null,function(r){return e.forEach(function(o){return Il(o,r)})});return fC(function(){var r=Rf.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(l){s.has(l)||Il(l,null)}),s.forEach(function(l){o.has(l)||Il(l,i)})}Rf.set(n,e)},[e]),n}function mC(e){return e}function hC(e,t){t===void 0&&(t=mC);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(l){return l!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(l){return s(l)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var l=n;n=[],l.forEach(s),i=n}var a=function(){var f=i;i=[],f.forEach(s)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(f){i.push(f),u()},filter:function(f){return i=i.filter(f),n}}}};return o}function gC(e){e===void 0&&(e={});var t=hC(null);return t.options=_t({async:!0,ssr:!1},e),t}var Sg=function(e){var t=e.sideCar,n=wg(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return g.createElement(r,_t({},n))};Sg.isSideCarExport=!0;function vC(e,t){return e.useMedium(t),Sg}var Eg=gC(),Fl=function(){},Gi=g.forwardRef(function(e,t){var n=g.useRef(null),r=g.useState({onScrollCapture:Fl,onWheelCapture:Fl,onTouchMoveCapture:Fl}),o=r[0],s=r[1],i=e.forwardProps,l=e.children,a=e.className,u=e.removeScrollBar,f=e.enabled,m=e.shards,y=e.sideCar,w=e.noRelative,S=e.noIsolation,p=e.inert,x=e.allowPinchZoom,h=e.as,d=h===void 0?"div":h,v=e.gapMode,E=wg(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=y,T=pC([n,t]),N=_t(_t({},E),o);return g.createElement(g.Fragment,null,f&&g.createElement(C,{sideCar:Eg,removeScrollBar:u,shards:m,noRelative:w,noIsolation:S,inert:p,setCallbacks:s,allowPinchZoom:!!x,lockRef:n,gapMode:v}),i?g.cloneElement(g.Children.only(l),_t(_t({},N),{ref:T})):g.createElement(d,_t({},N,{className:a,ref:T}),l))});Gi.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Gi.classNames={fullWidth:Is,zeroRight:Ms};var yC=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function xC(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=yC();return t&&e.setAttribute("nonce",t),e}function wC(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function SC(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var EC=function(){var e=0,t=null;return{add:function(n){e==0&&(t=xC())&&(wC(t,n),SC(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},CC=function(){var e=EC();return function(t,n){g.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Cg=function(){var e=CC(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},NC={left:0,top:0,right:0,gap:0},zl=function(e){return parseInt(e||"",10)||0},kC=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[zl(n),zl(r),zl(o)]},TC=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return NC;var t=kC(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},RC=Cg(),Cr="data-scroll-locked",PC=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(uC,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Cr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ms,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Is,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Ms," .").concat(Ms,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Is," .").concat(Is,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Cr,`] {
    `).concat(cC,": ").concat(l,`px;
  }
`)},Pf=function(){var e=parseInt(document.body.getAttribute(Cr)||"0",10);return isFinite(e)?e:0},bC=function(){g.useEffect(function(){return document.body.setAttribute(Cr,(Pf()+1).toString()),function(){var e=Pf()-1;e<=0?document.body.removeAttribute(Cr):document.body.setAttribute(Cr,e.toString())}},[])},_C=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;bC();var s=g.useMemo(function(){return TC(o)},[o]);return g.createElement(RC,{styles:PC(s,!t,o,n?"":"!important")})},Xa=!1;if(typeof window<"u")try{var gs=Object.defineProperty({},"passive",{get:function(){return Xa=!0,!0}});window.addEventListener("test",gs,gs),window.removeEventListener("test",gs,gs)}catch{Xa=!1}var Zn=Xa?{passive:!1}:!1,jC=function(e){return e.tagName==="TEXTAREA"},Ng=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!jC(e)&&n[t]==="visible")},AC=function(e){return Ng(e,"overflowY")},OC=function(e){return Ng(e,"overflowX")},bf=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=kg(e,r);if(o){var s=Tg(e,r),i=s[1],l=s[2];if(i>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},LC=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},DC=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},kg=function(e,t){return e==="v"?AC(t):OC(t)},Tg=function(e,t){return e==="v"?LC(t):DC(t)},MC=function(e,t){return e==="h"&&t==="rtl"?-1:1},IC=function(e,t,n,r,o){var s=MC(e,window.getComputedStyle(t).direction),i=s*r,l=n.target,a=t.contains(l),u=!1,f=i>0,m=0,y=0;do{var w=Tg(e,l),S=w[0],p=w[1],x=w[2],h=p-x-s*S;(S||h)&&kg(e,l)&&(m+=h,y+=S),l=l.parentNode.host||l.parentNode}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(f&&Math.abs(m)<1||!f&&Math.abs(y)<1)&&(u=!0),u},vs=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_f=function(e){return[e.deltaX,e.deltaY]},jf=function(e){return e&&"current"in e?e.current:e},FC=function(e,t){return e[0]===t[0]&&e[1]===t[1]},zC=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},$C=0,er=[];function UC(e){var t=g.useRef([]),n=g.useRef([0,0]),r=g.useRef(),o=g.useState($C++)[0],s=g.useState(Cg)[0],i=g.useRef(e);g.useEffect(function(){i.current=e},[e]),g.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=aC([e.lockRef.current],(e.shards||[]).map(jf),!0).filter(Boolean);return p.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=g.useCallback(function(p,x){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!i.current.allowPinchZoom;var h=vs(p),d=n.current,v="deltaX"in p?p.deltaX:d[0]-h[0],E="deltaY"in p?p.deltaY:d[1]-h[1],C,T=p.target,N=Math.abs(v)>Math.abs(E)?"h":"v";if("touches"in p&&N==="h"&&T.type==="range")return!1;var R=bf(N,T);if(!R)return!0;if(R?C=N:(C=N==="v"?"h":"v",R=bf(N,T)),!R)return!1;if(!r.current&&"changedTouches"in p&&(v||E)&&(r.current=C),!C)return!0;var j=r.current||C;return IC(j,x,p,j==="h"?v:E)},[]),a=g.useCallback(function(p){var x=p;if(!(!er.length||er[er.length-1]!==s)){var h="deltaY"in x?_f(x):vs(x),d=t.current.filter(function(C){return C.name===x.type&&(C.target===x.target||x.target===C.shadowParent)&&FC(C.delta,h)})[0];if(d&&d.should){x.cancelable&&x.preventDefault();return}if(!d){var v=(i.current.shards||[]).map(jf).filter(Boolean).filter(function(C){return C.contains(x.target)}),E=v.length>0?l(x,v[0]):!i.current.noIsolation;E&&x.cancelable&&x.preventDefault()}}},[]),u=g.useCallback(function(p,x,h,d){var v={name:p,delta:x,target:h,should:d,shadowParent:BC(h)};t.current.push(v),setTimeout(function(){t.current=t.current.filter(function(E){return E!==v})},1)},[]),f=g.useCallback(function(p){n.current=vs(p),r.current=void 0},[]),m=g.useCallback(function(p){u(p.type,_f(p),p.target,l(p,e.lockRef.current))},[]),y=g.useCallback(function(p){u(p.type,vs(p),p.target,l(p,e.lockRef.current))},[]);g.useEffect(function(){return er.push(s),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:y}),document.addEventListener("wheel",a,Zn),document.addEventListener("touchmove",a,Zn),document.addEventListener("touchstart",f,Zn),function(){er=er.filter(function(p){return p!==s}),document.removeEventListener("wheel",a,Zn),document.removeEventListener("touchmove",a,Zn),document.removeEventListener("touchstart",f,Zn)}},[]);var w=e.removeScrollBar,S=e.inert;return g.createElement(g.Fragment,null,S?g.createElement(s,{styles:zC(o)}):null,w?g.createElement(_C,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function BC(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const VC=vC(Eg,UC);var xc=g.forwardRef(function(e,t){return g.createElement(Gi,_t({},e,{ref:t,sideCar:VC}))});xc.classNames=Gi.classNames;var WC=[" ","Enter","ArrowUp","ArrowDown"],HC=[" ","Enter"],Hn="Select",[Xi,qi,KC]=Wh(Hn),[Ur,Lk]=Ho(Hn,[KC,ag]),Yi=ag(),[QC,kn]=Ur(Hn),[GC,XC]=Ur(Hn),Rg=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:l,onValueChange:a,dir:u,name:f,autoComplete:m,disabled:y,required:w,form:S}=e,p=Yi(t),[x,h]=g.useState(null),[d,v]=g.useState(null),[E,C]=g.useState(!1),T=NS(u),[N,R]=gi({prop:r,defaultProp:o??!1,onChange:s,caller:Hn}),[j,_]=gi({prop:i,defaultProp:l,onChange:a,caller:Hn}),I=g.useRef(null),L=x?S||!!x.closest("form"):!0,[B,O]=g.useState(new Set),$=Array.from(B).map(F=>F.props.value).join(";");return c.jsx(XE,{...p,children:c.jsxs(QC,{required:w,scope:t,trigger:x,onTriggerChange:h,valueNode:d,onValueNodeChange:v,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:Sr(),value:j,onValueChange:_,open:N,onOpenChange:R,dir:T,triggerPointerDownPosRef:I,disabled:y,children:[c.jsx(Xi.Provider,{scope:t,children:c.jsx(GC,{scope:e.__scopeSelect,onNativeOptionAdd:g.useCallback(F=>{O(W=>new Set(W).add(F))},[]),onNativeOptionRemove:g.useCallback(F=>{O(W=>{const P=new Set(W);return P.delete(F),P})},[]),children:n})}),L?c.jsxs(Yg,{"aria-hidden":!0,required:w,tabIndex:-1,name:f,autoComplete:m,value:j,onChange:F=>_(F.target.value),disabled:y,form:S,children:[j===void 0?c.jsx("option",{value:""}):null,Array.from(B)]},$):null]})})};Rg.displayName=Hn;var Pg="SelectTrigger",bg=g.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=Yi(n),i=kn(Pg,n),l=i.disabled||r,a=fe(t,i.onTriggerChange),u=qi(n),f=g.useRef("touch"),[m,y,w]=Zg(p=>{const x=u().filter(v=>!v.disabled),h=x.find(v=>v.value===i.value),d=ev(x,p,h);d!==void 0&&i.onValueChange(d.value)}),S=p=>{l||(i.onOpenChange(!0),w()),p&&(i.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return c.jsx(qE,{asChild:!0,...s,children:c.jsx(q.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Jg(i.value)?"":void 0,...o,ref:a,onClick:G(o.onClick,p=>{p.currentTarget.focus(),f.current!=="mouse"&&S(p)}),onPointerDown:G(o.onPointerDown,p=>{f.current=p.pointerType;const x=p.target;x.hasPointerCapture(p.pointerId)&&x.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(S(p),p.preventDefault())}),onKeyDown:G(o.onKeyDown,p=>{const x=m.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&y(p.key),!(x&&p.key===" ")&&WC.includes(p.key)&&(S(),p.preventDefault())})})})});bg.displayName=Pg;var _g="SelectValue",jg=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:i="",...l}=e,a=kn(_g,n),{onValueNodeHasChildrenChange:u}=a,f=s!==void 0,m=fe(t,a.onValueNodeChange);return Re(()=>{u(f)},[u,f]),c.jsx(q.span,{...l,ref:m,style:{pointerEvents:"none"},children:Jg(a.value)?c.jsx(c.Fragment,{children:i}):s})});jg.displayName=_g;var qC="SelectIcon",Ag=g.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return c.jsx(q.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});Ag.displayName=qC;var YC="SelectPortal",Og=e=>c.jsx(Qi,{asChild:!0,...e});Og.displayName=YC;var Kn="SelectContent",Lg=g.forwardRef((e,t)=>{const n=kn(Kn,e.__scopeSelect),[r,o]=g.useState();if(Re(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?qn.createPortal(c.jsx(Dg,{scope:e.__scopeSelect,children:c.jsx(Xi.Slot,{scope:e.__scopeSelect,children:c.jsx("div",{children:e.children})})}),s):null}return c.jsx(Mg,{...e,ref:t})});Lg.displayName=Kn;var ft=10,[Dg,Tn]=Ur(Kn),JC="SelectContentImpl",ZC=jr("SelectContent.RemoveScroll"),Mg=g.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:l,sideOffset:a,align:u,alignOffset:f,arrowPadding:m,collisionBoundary:y,collisionPadding:w,sticky:S,hideWhenDetached:p,avoidCollisions:x,...h}=e,d=kn(Kn,n),[v,E]=g.useState(null),[C,T]=g.useState(null),N=fe(t,z=>E(z)),[R,j]=g.useState(null),[_,I]=g.useState(null),L=qi(n),[B,O]=g.useState(!1),$=g.useRef(!1);g.useEffect(()=>{if(v)return xg(v)},[v]),Gh();const F=g.useCallback(z=>{const[oe,...Pe]=L().map(ee=>ee.ref.current),[te]=Pe.slice(-1),Z=document.activeElement;for(const ee of z)if(ee===Z||(ee==null||ee.scrollIntoView({block:"nearest"}),ee===oe&&C&&(C.scrollTop=0),ee===te&&C&&(C.scrollTop=C.scrollHeight),ee==null||ee.focus(),document.activeElement!==Z))return},[L,C]),W=g.useCallback(()=>F([R,v]),[F,R,v]);g.useEffect(()=>{B&&W()},[B,W]);const{onOpenChange:P,triggerPointerDownPosRef:A}=d;g.useEffect(()=>{if(v){let z={x:0,y:0};const oe=te=>{var Z,ee;z={x:Math.abs(Math.round(te.pageX)-(((Z=A.current)==null?void 0:Z.x)??0)),y:Math.abs(Math.round(te.pageY)-(((ee=A.current)==null?void 0:ee.y)??0))}},Pe=te=>{z.x<=10&&z.y<=10?te.preventDefault():v.contains(te.target)||P(!1),document.removeEventListener("pointermove",oe),A.current=null};return A.current!==null&&(document.addEventListener("pointermove",oe),document.addEventListener("pointerup",Pe,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",oe),document.removeEventListener("pointerup",Pe,{capture:!0})}}},[v,P,A]),g.useEffect(()=>{const z=()=>P(!1);return window.addEventListener("blur",z),window.addEventListener("resize",z),()=>{window.removeEventListener("blur",z),window.removeEventListener("resize",z)}},[P]);const[M,U]=Zg(z=>{const oe=L().filter(Z=>!Z.disabled),Pe=oe.find(Z=>Z.ref.current===document.activeElement),te=ev(oe,z,Pe);te&&setTimeout(()=>te.ref.current.focus())}),J=g.useCallback((z,oe,Pe)=>{const te=!$.current&&!Pe;(d.value!==void 0&&d.value===oe||te)&&(j(z),te&&($.current=!0))},[d.value]),Le=g.useCallback(()=>v==null?void 0:v.focus(),[v]),ve=g.useCallback((z,oe,Pe)=>{const te=!$.current&&!Pe;(d.value!==void 0&&d.value===oe||te)&&I(z)},[d.value]),Nt=r==="popper"?qa:Ig,De=Nt===qa?{side:l,sideOffset:a,align:u,alignOffset:f,arrowPadding:m,collisionBoundary:y,collisionPadding:w,sticky:S,hideWhenDetached:p,avoidCollisions:x}:{};return c.jsx(Dg,{scope:n,content:v,viewport:C,onViewportChange:T,itemRefCallback:J,selectedItem:R,onItemLeave:Le,itemTextRefCallback:ve,focusSelectedItem:W,selectedItemText:_,position:r,isPositioned:B,searchRef:M,children:c.jsx(xc,{as:ZC,allowPinchZoom:!0,children:c.jsx(ac,{asChild:!0,trapped:d.open,onMountAutoFocus:z=>{z.preventDefault()},onUnmountAutoFocus:G(o,z=>{var oe;(oe=d.trigger)==null||oe.focus({preventScroll:!0}),z.preventDefault()}),children:c.jsx(Vi,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:z=>z.preventDefault(),onDismiss:()=>d.onOpenChange(!1),children:c.jsx(Nt,{role:"listbox",id:d.contentId,"data-state":d.open?"open":"closed",dir:d.dir,onContextMenu:z=>z.preventDefault(),...h,...De,onPlaced:()=>O(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...h.style},onKeyDown:G(h.onKeyDown,z=>{const oe=z.ctrlKey||z.altKey||z.metaKey;if(z.key==="Tab"&&z.preventDefault(),!oe&&z.key.length===1&&U(z.key),["ArrowUp","ArrowDown","Home","End"].includes(z.key)){let te=L().filter(Z=>!Z.disabled).map(Z=>Z.ref.current);if(["ArrowUp","End"].includes(z.key)&&(te=te.slice().reverse()),["ArrowUp","ArrowDown"].includes(z.key)){const Z=z.target,ee=te.indexOf(Z);te=te.slice(ee+1)}setTimeout(()=>F(te)),z.preventDefault()}})})})})})})});Mg.displayName=JC;var eN="SelectItemAlignedPosition",Ig=g.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=kn(Kn,n),i=Tn(Kn,n),[l,a]=g.useState(null),[u,f]=g.useState(null),m=fe(t,N=>f(N)),y=qi(n),w=g.useRef(!1),S=g.useRef(!0),{viewport:p,selectedItem:x,selectedItemText:h,focusSelectedItem:d}=i,v=g.useCallback(()=>{if(s.trigger&&s.valueNode&&l&&u&&p&&x&&h){const N=s.trigger.getBoundingClientRect(),R=u.getBoundingClientRect(),j=s.valueNode.getBoundingClientRect(),_=h.getBoundingClientRect();if(s.dir!=="rtl"){const Z=_.left-R.left,ee=j.left-Z,Ge=N.left-ee,kt=N.width+Ge,Br=Math.max(kt,R.width),Vr=window.innerWidth-ft,Wr=df(ee,[ft,Math.max(ft,Vr-Br)]);l.style.minWidth=kt+"px",l.style.left=Wr+"px"}else{const Z=R.right-_.right,ee=window.innerWidth-j.right-Z,Ge=window.innerWidth-N.right-ee,kt=N.width+Ge,Br=Math.max(kt,R.width),Vr=window.innerWidth-ft,Wr=df(ee,[ft,Math.max(ft,Vr-Br)]);l.style.minWidth=kt+"px",l.style.right=Wr+"px"}const I=y(),L=window.innerHeight-ft*2,B=p.scrollHeight,O=window.getComputedStyle(u),$=parseInt(O.borderTopWidth,10),F=parseInt(O.paddingTop,10),W=parseInt(O.borderBottomWidth,10),P=parseInt(O.paddingBottom,10),A=$+F+B+P+W,M=Math.min(x.offsetHeight*5,A),U=window.getComputedStyle(p),J=parseInt(U.paddingTop,10),Le=parseInt(U.paddingBottom,10),ve=N.top+N.height/2-ft,Nt=L-ve,De=x.offsetHeight/2,z=x.offsetTop+De,oe=$+F+z,Pe=A-oe;if(oe<=ve){const Z=I.length>0&&x===I[I.length-1].ref.current;l.style.bottom="0px";const ee=u.clientHeight-p.offsetTop-p.offsetHeight,Ge=Math.max(Nt,De+(Z?Le:0)+ee+W),kt=oe+Ge;l.style.height=kt+"px"}else{const Z=I.length>0&&x===I[0].ref.current;l.style.top="0px";const Ge=Math.max(ve,$+p.offsetTop+(Z?J:0)+De)+Pe;l.style.height=Ge+"px",p.scrollTop=oe-ve+p.offsetTop}l.style.margin=`${ft}px 0`,l.style.minHeight=M+"px",l.style.maxHeight=L+"px",r==null||r(),requestAnimationFrame(()=>w.current=!0)}},[y,s.trigger,s.valueNode,l,u,p,x,h,s.dir,r]);Re(()=>v(),[v]);const[E,C]=g.useState();Re(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const T=g.useCallback(N=>{N&&S.current===!0&&(v(),d==null||d(),S.current=!1)},[v,d]);return c.jsx(nN,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:w,onScrollButtonChange:T,children:c.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:c.jsx(q.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});Ig.displayName=eN;var tN="SelectPopperPosition",qa=g.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=ft,...s}=e,i=Yi(n);return c.jsx(YE,{...i,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});qa.displayName=tN;var[nN,wc]=Ur(Kn,{}),Ya="SelectViewport",Fg=g.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=Tn(Ya,n),i=wc(Ya,n),l=fe(t,s.onViewportChange),a=g.useRef(0);return c.jsxs(c.Fragment,{children:[c.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),c.jsx(Xi.Slot,{scope:n,children:c.jsx(q.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:G(o.onScroll,u=>{const f=u.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:y}=i;if(y!=null&&y.current&&m){const w=Math.abs(a.current-f.scrollTop);if(w>0){const S=window.innerHeight-ft*2,p=parseFloat(m.style.minHeight),x=parseFloat(m.style.height),h=Math.max(p,x);if(h<S){const d=h+w,v=Math.min(S,d),E=d-v;m.style.height=v+"px",m.style.bottom==="0px"&&(f.scrollTop=E>0?E:0,m.style.justifyContent="flex-end")}}}a.current=f.scrollTop})})})]})});Fg.displayName=Ya;var zg="SelectGroup",[rN,oN]=Ur(zg),sN=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Sr();return c.jsx(rN,{scope:n,id:o,children:c.jsx(q.div,{role:"group","aria-labelledby":o,...r,ref:t})})});sN.displayName=zg;var $g="SelectLabel",Ug=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=oN($g,n);return c.jsx(q.div,{id:o.id,...r,ref:t})});Ug.displayName=$g;var vi="SelectItem",[iN,Bg]=Ur(vi),Vg=g.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...i}=e,l=kn(vi,n),a=Tn(vi,n),u=l.value===r,[f,m]=g.useState(s??""),[y,w]=g.useState(!1),S=fe(t,d=>{var v;return(v=a.itemRefCallback)==null?void 0:v.call(a,d,r,o)}),p=Sr(),x=g.useRef("touch"),h=()=>{o||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return c.jsx(iN,{scope:n,value:r,disabled:o,textId:p,isSelected:u,onItemTextChange:g.useCallback(d=>{m(v=>v||((d==null?void 0:d.textContent)??"").trim())},[]),children:c.jsx(Xi.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:c.jsx(q.div,{role:"option","aria-labelledby":p,"data-highlighted":y?"":void 0,"aria-selected":u&&y,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:S,onFocus:G(i.onFocus,()=>w(!0)),onBlur:G(i.onBlur,()=>w(!1)),onClick:G(i.onClick,()=>{x.current!=="mouse"&&h()}),onPointerUp:G(i.onPointerUp,()=>{x.current==="mouse"&&h()}),onPointerDown:G(i.onPointerDown,d=>{x.current=d.pointerType}),onPointerMove:G(i.onPointerMove,d=>{var v;x.current=d.pointerType,o?(v=a.onItemLeave)==null||v.call(a):x.current==="mouse"&&d.currentTarget.focus({preventScroll:!0})}),onPointerLeave:G(i.onPointerLeave,d=>{var v;d.currentTarget===document.activeElement&&((v=a.onItemLeave)==null||v.call(a))}),onKeyDown:G(i.onKeyDown,d=>{var E;((E=a.searchRef)==null?void 0:E.current)!==""&&d.key===" "||(HC.includes(d.key)&&h(),d.key===" "&&d.preventDefault())})})})})});Vg.displayName=vi;var io="SelectItemText",Wg=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,i=kn(io,n),l=Tn(io,n),a=Bg(io,n),u=XC(io,n),[f,m]=g.useState(null),y=fe(t,h=>m(h),a.onItemTextChange,h=>{var d;return(d=l.itemTextRefCallback)==null?void 0:d.call(l,h,a.value,a.disabled)}),w=f==null?void 0:f.textContent,S=g.useMemo(()=>c.jsx("option",{value:a.value,disabled:a.disabled,children:w},a.value),[a.disabled,a.value,w]),{onNativeOptionAdd:p,onNativeOptionRemove:x}=u;return Re(()=>(p(S),()=>x(S)),[p,x,S]),c.jsxs(c.Fragment,{children:[c.jsx(q.span,{id:a.textId,...s,ref:y}),a.isSelected&&i.valueNode&&!i.valueNodeHasChildren?qn.createPortal(s.children,i.valueNode):null]})});Wg.displayName=io;var Hg="SelectItemIndicator",Kg=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Bg(Hg,n).isSelected?c.jsx(q.span,{"aria-hidden":!0,...r,ref:t}):null});Kg.displayName=Hg;var Ja="SelectScrollUpButton",Qg=g.forwardRef((e,t)=>{const n=Tn(Ja,e.__scopeSelect),r=wc(Ja,e.__scopeSelect),[o,s]=g.useState(!1),i=fe(t,r.onScrollButtonChange);return Re(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=a.scrollTop>0;s(u)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?c.jsx(Xg,{...e,ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop-a.offsetHeight)}}):null});Qg.displayName=Ja;var Za="SelectScrollDownButton",Gg=g.forwardRef((e,t)=>{const n=Tn(Za,e.__scopeSelect),r=wc(Za,e.__scopeSelect),[o,s]=g.useState(!1),i=fe(t,r.onScrollButtonChange);return Re(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=a.scrollHeight-a.clientHeight,f=Math.ceil(a.scrollTop)<u;s(f)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?c.jsx(Xg,{...e,ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop+a.offsetHeight)}}):null});Gg.displayName=Za;var Xg=g.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=Tn("SelectScrollButton",n),i=g.useRef(null),l=qi(n),a=g.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return g.useEffect(()=>()=>a(),[a]),Re(()=>{var f;const u=l().find(m=>m.ref.current===document.activeElement);(f=u==null?void 0:u.ref.current)==null||f.scrollIntoView({block:"nearest"})},[l]),c.jsx(q.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:G(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:G(o.onPointerMove,()=>{var u;(u=s.onItemLeave)==null||u.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:G(o.onPointerLeave,()=>{a()})})}),lN="SelectSeparator",qg=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return c.jsx(q.div,{"aria-hidden":!0,...r,ref:t})});qg.displayName=lN;var eu="SelectArrow",aN=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Yi(n),s=kn(eu,n),i=Tn(eu,n);return s.open&&i.position==="popper"?c.jsx(JE,{...o,...r,ref:t}):null});aN.displayName=eu;var uN="SelectBubbleInput",Yg=g.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const o=g.useRef(null),s=fe(r,o),i=rC(t);return g.useEffect(()=>{const l=o.current;if(!l)return;const a=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(a,"value").set;if(i!==t&&f){const m=new Event("change",{bubbles:!0});f.call(l,t),l.dispatchEvent(m)}},[i,t]),c.jsx(q.select,{...n,style:{...vg,...n.style},ref:s,defaultValue:t})});Yg.displayName=uN;function Jg(e){return e===""||e===void 0}function Zg(e){const t=xt(e),n=g.useRef(""),r=g.useRef(0),o=g.useCallback(i=>{const l=n.current+i;t(l),function a(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(l)},[t]),s=g.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function ev(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=cN(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const a=i.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function cN(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var dN=Rg,tv=bg,fN=jg,pN=Ag,mN=Og,nv=Lg,hN=Fg,rv=Ug,ov=Vg,gN=Wg,vN=Kg,sv=Qg,iv=Gg,lv=qg;const av=dN,uv=fN,Sc=g.forwardRef(({className:e,children:t,...n},r)=>c.jsxs(tv,{ref:r,className:K("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,c.jsx(pN,{asChild:!0,children:c.jsx(bh,{className:"h-4 w-4 opacity-50"})})]}));Sc.displayName=tv.displayName;const cv=g.forwardRef(({className:e,...t},n)=>c.jsx(sv,{ref:n,className:K("flex cursor-default items-center justify-center py-1",e),...t,children:c.jsx(nS,{className:"h-4 w-4"})}));cv.displayName=sv.displayName;const dv=g.forwardRef(({className:e,...t},n)=>c.jsx(iv,{ref:n,className:K("flex cursor-default items-center justify-center py-1",e),...t,children:c.jsx(bh,{className:"h-4 w-4"})}));dv.displayName=iv.displayName;const Ec=g.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>c.jsx(mN,{children:c.jsxs(nv,{ref:o,className:K("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[c.jsx(cv,{}),c.jsx(hN,{className:K("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),c.jsx(dv,{})]})}));Ec.displayName=nv.displayName;const yN=g.forwardRef(({className:e,...t},n)=>c.jsx(rv,{ref:n,className:K("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));yN.displayName=rv.displayName;const Cc=g.forwardRef(({className:e,children:t,...n},r)=>c.jsxs(ov,{ref:r,className:K("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[c.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:c.jsx(vN,{children:c.jsx(tS,{className:"h-4 w-4"})})}),c.jsx(gN,{children:t})]}));Cc.displayName=ov.displayName;const xN=g.forwardRef(({className:e,...t},n)=>c.jsx(lv,{ref:n,className:K("-mx-1 my-1 h-px bg-muted",e),...t}));xN.displayName=lv.displayName;function wN(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Qo=e=>{const{present:t,children:n}=e,r=SN(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),s=fe(r.ref,EN(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:s}):null};Qo.displayName="Presence";function SN(e){const[t,n]=g.useState(),r=g.useRef(null),o=g.useRef(e),s=g.useRef("none"),i=e?"mounted":"unmounted",[l,a]=wN(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=ys(r.current);s.current=l==="mounted"?u:"none"},[l]),Re(()=>{const u=r.current,f=o.current;if(f!==e){const y=s.current,w=ys(u);e?a("MOUNT"):w==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(f&&y!==w?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),Re(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,m=w=>{const p=ys(r.current).includes(w.animationName);if(w.target===t&&p&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},y=w=>{w.target===t&&(s.current=ys(r.current))};return t.addEventListener("animationstart",y),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",y),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:g.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function ys(e){return(e==null?void 0:e.animationName)||"none"}function EN(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ji="Dialog",[fv,Dk]=Ho(Ji),[CN,Ct]=fv(Ji),pv=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,l=g.useRef(null),a=g.useRef(null),[u,f]=gi({prop:r,defaultProp:o??!1,onChange:s,caller:Ji});return c.jsx(CN,{scope:t,triggerRef:l,contentRef:a,contentId:Sr(),titleId:Sr(),descriptionId:Sr(),open:u,onOpenChange:f,onOpenToggle:g.useCallback(()=>f(m=>!m),[f]),modal:i,children:n})};pv.displayName=Ji;var mv="DialogTrigger",hv=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ct(mv,n),s=fe(t,o.triggerRef);return c.jsx(q.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Tc(o.open),...r,ref:s,onClick:G(e.onClick,o.onOpenToggle)})});hv.displayName=mv;var Nc="DialogPortal",[NN,gv]=fv(Nc,{forceMount:void 0}),vv=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Ct(Nc,t);return c.jsx(NN,{scope:t,forceMount:n,children:g.Children.map(r,i=>c.jsx(Qo,{present:n||s.open,children:c.jsx(Qi,{asChild:!0,container:o,children:i})}))})};vv.displayName=Nc;var yi="DialogOverlay",yv=g.forwardRef((e,t)=>{const n=gv(yi,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ct(yi,e.__scopeDialog);return s.modal?c.jsx(Qo,{present:r||s.open,children:c.jsx(TN,{...o,ref:t})}):null});yv.displayName=yi;var kN=jr("DialogOverlay.RemoveScroll"),TN=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ct(yi,n);return c.jsx(xc,{as:kN,allowPinchZoom:!0,shards:[o.contentRef],children:c.jsx(q.div,{"data-state":Tc(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Qn="DialogContent",xv=g.forwardRef((e,t)=>{const n=gv(Qn,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ct(Qn,e.__scopeDialog);return c.jsx(Qo,{present:r||s.open,children:s.modal?c.jsx(RN,{...o,ref:t}):c.jsx(PN,{...o,ref:t})})});xv.displayName=Qn;var RN=g.forwardRef((e,t)=>{const n=Ct(Qn,e.__scopeDialog),r=g.useRef(null),o=fe(t,n.contentRef,r);return g.useEffect(()=>{const s=r.current;if(s)return xg(s)},[]),c.jsx(wv,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:G(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:G(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,l=i.button===0&&i.ctrlKey===!0;(i.button===2||l)&&s.preventDefault()}),onFocusOutside:G(e.onFocusOutside,s=>s.preventDefault())})}),PN=g.forwardRef((e,t)=>{const n=Ct(Qn,e.__scopeDialog),r=g.useRef(!1),o=g.useRef(!1);return c.jsx(wv,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,l;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var a,u;(a=e.onInteractOutside)==null||a.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),wv=g.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,l=Ct(Qn,n),a=g.useRef(null),u=fe(t,a);return Gh(),c.jsxs(c.Fragment,{children:[c.jsx(ac,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:c.jsx(Vi,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Tc(l.open),...i,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),c.jsxs(c.Fragment,{children:[c.jsx(bN,{titleId:l.titleId}),c.jsx(jN,{contentRef:a,descriptionId:l.descriptionId})]})]})}),kc="DialogTitle",Sv=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ct(kc,n);return c.jsx(q.h2,{id:o.titleId,...r,ref:t})});Sv.displayName=kc;var Ev="DialogDescription",Cv=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ct(Ev,n);return c.jsx(q.p,{id:o.descriptionId,...r,ref:t})});Cv.displayName=Ev;var Nv="DialogClose",kv=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ct(Nv,n);return c.jsx(q.button,{type:"button",...r,ref:t,onClick:G(e.onClick,()=>o.onOpenChange(!1))})});kv.displayName=Nv;function Tc(e){return e?"open":"closed"}var Tv="DialogTitleWarning",[Mk,Rv]=SS(Tv,{contentName:Qn,titleName:kc,docsSlug:"dialog"}),bN=({titleId:e})=>{const t=Rv(Tv),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return g.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},_N="DialogDescriptionWarning",jN=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Rv(_N).contentName}}.`;return g.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},AN=pv,ON=hv,LN=vv,Pv=yv,bv=xv,_v=Sv,jv=Cv,DN=kv;const Av=AN,MN=ON,IN=LN,Ov=g.forwardRef(({className:e,...t},n)=>c.jsx(Pv,{ref:n,className:K("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Ov.displayName=Pv.displayName;const Rc=g.forwardRef(({className:e,children:t,...n},r)=>c.jsxs(IN,{children:[c.jsx(Ov,{}),c.jsxs(bv,{ref:r,className:K("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,c.jsxs(DN,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[c.jsx(lc,{className:"h-4 w-4"}),c.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Rc.displayName=bv.displayName;const Pc=({className:e,...t})=>c.jsx("div",{className:K("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Pc.displayName="DialogHeader";const bc=({className:e,...t})=>c.jsx("div",{className:K("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});bc.displayName="DialogFooter";const _c=g.forwardRef(({className:e,...t},n)=>c.jsx(_v,{ref:n,className:K("text-lg font-semibold leading-none tracking-tight",e),...t}));_c.displayName=_v.displayName;const jc=g.forwardRef(({className:e,...t},n)=>c.jsx(jv,{ref:n,className:K("text-sm text-muted-foreground",e),...t}));jc.displayName=jv.displayName;const FN=({tags:e,onCreateTag:t,loading:n})=>{const{filters:r,setFilters:o}=el(),[s,i]=g.useState(!1),[l,a]=g.useState(""),[u,f]=g.useState("#2563EB"),m=[{value:"",label:"All Statuses"},{value:"Applied",label:"Applied"},{value:"Interviewing",label:"Interviewing"},{value:"Offer",label:"Offer"},{value:"Rejected",label:"Rejected"},{value:"Withdrawn",label:"Withdrawn"}],y=["#2563EB","#DC2626","#059669","#D97706","#7C3AED","#DB2777","#0891B2","#65A30D","#DC2626","#9333EA"],w=(d,v)=>{o(E=>({...E,[d]:v}))},S=d=>{o(v=>({...v,tags:v.tags.includes(d)?v.tags.filter(E=>E!==d):[...v.tags,d]}))},p=async d=>{if(d.preventDefault(),!!l.trim())try{await t({name:l.trim(),color:u}),a(""),f("#2563EB"),i(!1)}catch(v){console.error("Failed to create tag:",v)}},x=()=>{o({status:"",search:"",tags:[],company:"",dateFrom:"",dateTo:""})},h=Object.values(r).some(d=>Array.isArray(d)?d.length>0:d!=="");return c.jsx(jt,{className:"mb-6",children:c.jsx(Vt,{className:"p-6",children:c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[c.jsxs("div",{className:"relative",children:[c.jsx(aS,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),c.jsx(Ie,{placeholder:"Search jobs...",value:r.search,onChange:d=>w("search",d.target.value),className:"pl-10"})]}),c.jsxs(av,{value:r.status,onValueChange:d=>w("status",d),children:[c.jsx(Sc,{children:c.jsx(uv,{placeholder:"Filter by status"})}),c.jsx(Ec,{children:m.map(d=>c.jsx(Cc,{value:d.value,children:d.label},d.value))})]}),c.jsxs("div",{className:"relative",children:[c.jsx(rc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),c.jsx(Ie,{placeholder:"Filter by company...",value:r.company,onChange:d=>w("company",d.target.value),className:"pl-10"})]}),c.jsxs(Ee,{variant:"outline",onClick:x,disabled:!h,className:"w-full",children:[c.jsx(lc,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{children:[c.jsx(we,{htmlFor:"dateFrom",className:"text-sm font-medium mb-2 block",children:"Applied From"}),c.jsx(Ie,{id:"dateFrom",type:"date",value:r.dateFrom,onChange:d=>w("dateFrom",d.target.value)})]}),c.jsxs("div",{children:[c.jsx(we,{htmlFor:"dateTo",className:"text-sm font-medium mb-2 block",children:"Applied To"}),c.jsx(Ie,{id:"dateTo",type:"date",value:r.dateTo,onChange:d=>w("dateTo",d.target.value)})]})]}),c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center justify-between mb-3",children:[c.jsx(we,{className:"text-sm font-medium",children:"Filter by Tags"}),c.jsxs(Av,{open:s,onOpenChange:i,children:[c.jsx(MN,{asChild:!0,children:c.jsxs(Ee,{variant:"outline",size:"sm",children:[c.jsx(Lh,{className:"h-4 w-4 mr-2"}),"New Tag"]})}),c.jsx(Rc,{className:"sm:max-w-[425px]",children:c.jsxs("form",{onSubmit:p,children:[c.jsxs(Pc,{children:[c.jsx(_c,{children:"Create New Tag"}),c.jsx(jc,{children:"Add a new tag to categorize your job applications."})]}),c.jsxs("div",{className:"grid gap-4 py-4",children:[c.jsxs("div",{children:[c.jsx(we,{htmlFor:"tagName",className:"text-sm font-medium",children:"Tag Name"}),c.jsx(Ie,{id:"tagName",value:l,onChange:d=>a(d.target.value),placeholder:"Enter tag name...",className:"mt-1"})]}),c.jsxs("div",{children:[c.jsx(we,{className:"text-sm font-medium mb-2 block",children:"Tag Color"}),c.jsx("div",{className:"flex flex-wrap gap-2",children:y.map(d=>c.jsx("button",{type:"button",onClick:()=>f(d),className:`w-8 h-8 rounded-full border-2 ${u===d?"border-foreground":"border-border"}`,style:{backgroundColor:d}},d))}),c.jsx(Ie,{type:"color",value:u,onChange:d=>f(d.target.value),className:"mt-2 w-full h-10"})]})]}),c.jsx(bc,{children:c.jsx(Ee,{type:"submit",disabled:!l.trim(),children:"Create Tag"})})]})})]})]}),c.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.map(d=>c.jsxs(Ye,{variant:"outline",className:`cursor-pointer transition-all ${r.tags.includes(d.id)?"ring-2 ring-primary":"hover:bg-accent"}`,style:{backgroundColor:r.tags.includes(d.id)?`${d.color}20`:`${d.color}10`,color:d.color,borderColor:`${d.color}40`},onClick:()=>S(d.id),children:[c.jsx(Dh,{className:"h-3 w-3 mr-1"}),d.name]},d.id)),e.length===0&&c.jsx("p",{className:"text-sm text-muted-foreground",children:"No tags available. Create your first tag to get started."})]})]}),h&&c.jsx("div",{className:"pt-4 border-t border-border",children:c.jsxs("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[c.jsx(iS,{className:"h-4 w-4"}),c.jsx("span",{children:"Active filters:"}),r.status&&c.jsx(Ye,{variant:"secondary",children:r.status}),r.search&&c.jsxs(Ye,{variant:"secondary",children:["Search: ",r.search]}),r.company&&c.jsxs(Ye,{variant:"secondary",children:["Company: ",r.company]}),r.tags.length>0&&c.jsxs(Ye,{variant:"secondary",children:[r.tags.length," tags"]}),(r.dateFrom||r.dateTo)&&c.jsx(Ye,{variant:"secondary",children:"Date range"})]})})]})})})},tu=g.forwardRef(({className:e,...t},n)=>c.jsx("textarea",{className:K("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));tu.displayName="Textarea";const zN=1,$N=1e6;let $l=0;function UN(){return $l=($l+1)%Number.MAX_SAFE_INTEGER,$l.toString()}const Ul=new Map,Af=e=>{if(Ul.has(e))return;const t=setTimeout(()=>{Ul.delete(e),vo({type:"REMOVE_TOAST",toastId:e})},$N);Ul.set(e,t)},BN=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,zN)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Af(n):e.toasts.forEach(r=>{Af(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Fs=[];let zs={toasts:[]};function vo(e){zs=BN(zs,e),Fs.forEach(t=>{t(zs)})}function VN({...e}){const t=UN(),n=o=>vo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>vo({type:"DISMISS_TOAST",toastId:t});return vo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Ac(){const[e,t]=g.useState(zs);return g.useEffect(()=>(Fs.push(t),()=>{const n=Fs.indexOf(t);n>-1&&Fs.splice(n,1)}),[e]),{...e,toast:VN,dismiss:n=>vo({type:"DISMISS_TOAST",toastId:n})}}const WN=({job:e,tags:t,onSubmit:n,onClose:r,loading:o})=>{const{toast:s}=Ac(),[i,l]=g.useState({company:"",position:"",location:"",job_url:"",description:"",status:"Applied",salary_range:"",application_date:new Date().toISOString().split("T")[0],notes:"",contact_person:"",contact_email:"",tag_ids:[]}),[a,u]=g.useState({});g.useEffect(()=>{e&&l({company:e.company||"",position:e.position||"",location:e.location||"",job_url:e.job_url||"",description:e.description||"",status:e.status||"Applied",salary_range:e.salary_range||"",application_date:e.application_date?e.application_date.split("T")[0]:new Date().toISOString().split("T")[0],notes:e.notes||"",contact_person:e.contact_person||"",contact_email:e.contact_email||"",tag_ids:e.tags?e.tags.map(p=>p.id):[]})},[e]);const f=["Applied","Interviewing","Offer","Rejected","Withdrawn"],m=()=>{const p={};return i.company.trim()||(p.company="Company is required"),i.position.trim()||(p.position="Position is required"),i.contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i.contact_email)&&(p.contact_email="Please enter a valid email address"),i.job_url&&!/^https?:\/\/.+/.test(i.job_url)&&(p.job_url="Please enter a valid URL (starting with http:// or https://)"),u(p),Object.keys(p).length===0},y=(p,x)=>{l(h=>({...h,[p]:x})),a[p]&&u(h=>({...h,[p]:""}))},w=p=>{l(x=>({...x,tag_ids:x.tag_ids.includes(p)?x.tag_ids.filter(h=>h!==p):[...x.tag_ids,p]}))},S=async p=>{if(p.preventDefault(),!m()){s({title:"Validation Error",description:"Please fix the errors in the form",variant:"destructive"});return}try{await n(i)}catch(x){console.error("Form submission error:",x)}};return c.jsx(Av,{open:!0,onOpenChange:r,children:c.jsxs(Rc,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[c.jsxs(Pc,{children:[c.jsx(_c,{children:e?"Edit Job Application":"New Job Application"}),c.jsx(jc,{children:e?"Update the details of your job application.":"Add a new job application to track your progress."})]}),c.jsxs("form",{onSubmit:S,className:"space-y-6",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"company",className:"flex items-center",children:[c.jsx(rc,{className:"h-4 w-4 mr-2"}),"Company *"]}),c.jsx(Ie,{id:"company",value:i.company,onChange:p=>y("company",p.target.value),placeholder:"Enter company name",className:a.company?"border-destructive":""}),a.company&&c.jsx("p",{className:"text-sm text-destructive",children:a.company})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"position",className:"flex items-center",children:[c.jsx(Ih,{className:"h-4 w-4 mr-2"}),"Position *"]}),c.jsx(Ie,{id:"position",value:i.position,onChange:p=>y("position",p.target.value),placeholder:"Enter position title",className:a.position?"border-destructive":""}),a.position&&c.jsx("p",{className:"text-sm text-destructive",children:a.position})]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"location",className:"flex items-center",children:[c.jsx(Ah,{className:"h-4 w-4 mr-2"}),"Location"]}),c.jsx(Ie,{id:"location",value:i.location,onChange:p=>y("location",p.target.value),placeholder:"Enter location"})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"salary_range",className:"flex items-center",children:[c.jsx(_h,{className:"h-4 w-4 mr-2"}),"Salary Range"]}),c.jsx(Ie,{id:"salary_range",value:i.salary_range,onChange:p=>y("salary_range",p.target.value),placeholder:"e.g., $80,000 - $100,000"})]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx(we,{htmlFor:"status",children:"Status"}),c.jsxs(av,{value:i.status,onValueChange:p=>y("status",p),children:[c.jsx(Sc,{children:c.jsx(uv,{})}),c.jsx(Ec,{children:f.map(p=>c.jsx(Cc,{value:p,children:p},p))})]})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"application_date",className:"flex items-center",children:[c.jsx(oc,{className:"h-4 w-4 mr-2"}),"Application Date"]}),c.jsx(Ie,{id:"application_date",type:"date",value:i.application_date,onChange:p=>y("application_date",p.target.value)})]})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"job_url",className:"flex items-center",children:[c.jsx(sc,{className:"h-4 w-4 mr-2"}),"Job URL"]}),c.jsx(Ie,{id:"job_url",value:i.job_url,onChange:p=>y("job_url",p.target.value),placeholder:"https://...",className:a.job_url?"border-destructive":""}),a.job_url&&c.jsx("p",{className:"text-sm text-destructive",children:a.job_url})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx(we,{htmlFor:"contact_person",children:"Contact Person"}),c.jsx(Ie,{id:"contact_person",value:i.contact_person,onChange:p=>y("contact_person",p.target.value),placeholder:"Enter contact name"})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"contact_email",className:"flex items-center",children:[c.jsx(ic,{className:"h-4 w-4 mr-2"}),"Contact Email"]}),c.jsx(Ie,{id:"contact_email",type:"email",value:i.contact_email,onChange:p=>y("contact_email",p.target.value),placeholder:"<EMAIL>",className:a.contact_email?"border-destructive":""}),a.contact_email&&c.jsx("p",{className:"text-sm text-destructive",children:a.contact_email})]})]})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{htmlFor:"description",className:"flex items-center",children:[c.jsx(oS,{className:"h-4 w-4 mr-2"}),"Job Description"]}),c.jsx(tu,{id:"description",value:i.description,onChange:p=>y("description",p.target.value),placeholder:"Enter job description...",rows:3})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsx(we,{htmlFor:"notes",children:"Notes"}),c.jsx(tu,{id:"notes",value:i.notes,onChange:p=>y("notes",p.target.value),placeholder:"Add any additional notes...",rows:3})]})]}),c.jsxs("div",{className:"space-y-2",children:[c.jsxs(we,{className:"flex items-center",children:[c.jsx(Dh,{className:"h-4 w-4 mr-2"}),"Tags"]}),c.jsxs("div",{className:"flex flex-wrap gap-2 p-3 border border-input rounded-md min-h-[60px]",children:[t.map(p=>c.jsx(Ye,{variant:"outline",className:`cursor-pointer transition-all ${i.tag_ids.includes(p.id)?"ring-2 ring-primary":"hover:bg-accent"}`,style:{backgroundColor:i.tag_ids.includes(p.id)?`${p.color}20`:`${p.color}10`,color:p.color,borderColor:`${p.color}40`},onClick:()=>w(p.id),children:p.name},p.id)),t.length===0&&c.jsx("p",{className:"text-sm text-muted-foreground",children:"No tags available. Create tags in the filter bar to categorize your applications."})]})]}),c.jsxs(bc,{className:"gap-2",children:[c.jsx(Ee,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),c.jsxs(Ee,{type:"submit",disabled:o,children:[o&&c.jsx(jh,{className:"h-4 w-4 mr-2 animate-spin"}),e?"Update Application":"Create Application"]})]})]})]})})};var Oc="ToastProvider",[Lc,HN,KN]=Wh("Toast"),[Lv,Ik]=Ho("Toast",[KN]),[QN,Zi]=Lv(Oc),Dv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[l,a]=g.useState(null),[u,f]=g.useState(0),m=g.useRef(!1),y=g.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Oc}\`. Expected non-empty \`string\`.`),c.jsx(Lc.Provider,{scope:t,children:c.jsx(QN,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:g.useCallback(()=>f(w=>w+1),[]),onToastRemove:g.useCallback(()=>f(w=>w-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:y,children:i})})};Dv.displayName=Oc;var Mv="ToastViewport",GN=["F8"],nu="toast.viewportPause",ru="toast.viewportResume",Iv=g.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=GN,label:o="Notifications ({hotkey})",...s}=e,i=Zi(Mv,n),l=HN(n),a=g.useRef(null),u=g.useRef(null),f=g.useRef(null),m=g.useRef(null),y=fe(t,m,i.onViewportChange),w=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),S=i.toastCount>0;g.useEffect(()=>{const x=h=>{var v;r.length!==0&&r.every(E=>h[E]||h.code===E)&&((v=m.current)==null||v.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),g.useEffect(()=>{const x=a.current,h=m.current;if(S&&x&&h){const d=()=>{if(!i.isClosePausedRef.current){const T=new CustomEvent(nu);h.dispatchEvent(T),i.isClosePausedRef.current=!0}},v=()=>{if(i.isClosePausedRef.current){const T=new CustomEvent(ru);h.dispatchEvent(T),i.isClosePausedRef.current=!1}},E=T=>{!x.contains(T.relatedTarget)&&v()},C=()=>{x.contains(document.activeElement)||v()};return x.addEventListener("focusin",d),x.addEventListener("focusout",E),x.addEventListener("pointermove",d),x.addEventListener("pointerleave",C),window.addEventListener("blur",d),window.addEventListener("focus",v),()=>{x.removeEventListener("focusin",d),x.removeEventListener("focusout",E),x.removeEventListener("pointermove",d),x.removeEventListener("pointerleave",C),window.removeEventListener("blur",d),window.removeEventListener("focus",v)}}},[S,i.isClosePausedRef]);const p=g.useCallback(({tabbingDirection:x})=>{const d=l().map(v=>{const E=v.ref.current,C=[E,...lk(E)];return x==="forwards"?C:C.reverse()});return(x==="forwards"?d.reverse():d).flat()},[l]);return g.useEffect(()=>{const x=m.current;if(x){const h=d=>{var C,T,N;const v=d.altKey||d.ctrlKey||d.metaKey;if(d.key==="Tab"&&!v){const R=document.activeElement,j=d.shiftKey;if(d.target===x&&j){(C=u.current)==null||C.focus();return}const L=p({tabbingDirection:j?"backwards":"forwards"}),B=L.findIndex(O=>O===R);Bl(L.slice(B+1))?d.preventDefault():j?(T=u.current)==null||T.focus():(N=f.current)==null||N.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[l,p]),c.jsxs(OS,{ref:a,role:"region","aria-label":o.replace("{hotkey}",w),tabIndex:-1,style:{pointerEvents:S?void 0:"none"},children:[S&&c.jsx(ou,{ref:u,onFocusFromOutsideViewport:()=>{const x=p({tabbingDirection:"forwards"});Bl(x)}}),c.jsx(Lc.Slot,{scope:n,children:c.jsx(q.ol,{tabIndex:-1,...s,ref:y})}),S&&c.jsx(ou,{ref:f,onFocusFromOutsideViewport:()=>{const x=p({tabbingDirection:"backwards"});Bl(x)}})]})});Iv.displayName=Mv;var Fv="ToastFocusProxy",ou=g.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=Zi(Fv,n);return c.jsx(yc,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var u;const l=i.relatedTarget;!((u=s.viewport)!=null&&u.contains(l))&&r()}})});ou.displayName=Fv;var Go="Toast",XN="toast.swipeStart",qN="toast.swipeMove",YN="toast.swipeCancel",JN="toast.swipeEnd",zv=g.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[l,a]=gi({prop:r,defaultProp:o??!0,onChange:s,caller:Go});return c.jsx(Qo,{present:n||l,children:c.jsx(tk,{open:l,...i,ref:t,onClose:()=>a(!1),onPause:xt(e.onPause),onResume:xt(e.onResume),onSwipeStart:G(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:G(e.onSwipeMove,u=>{const{x:f,y:m}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${m}px`)}),onSwipeCancel:G(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:G(e.onSwipeEnd,u=>{const{x:f,y:m}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${m}px`),a(!1)})})})});zv.displayName=Go;var[ZN,ek]=Lv(Go,{onClose(){}}),tk=g.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:f,onSwipeMove:m,onSwipeCancel:y,onSwipeEnd:w,...S}=e,p=Zi(Go,n),[x,h]=g.useState(null),d=fe(t,O=>h(O)),v=g.useRef(null),E=g.useRef(null),C=o||p.duration,T=g.useRef(0),N=g.useRef(C),R=g.useRef(0),{onToastAdd:j,onToastRemove:_}=p,I=xt(()=>{var $;(x==null?void 0:x.contains(document.activeElement))&&(($=p.viewport)==null||$.focus()),i()}),L=g.useCallback(O=>{!O||O===1/0||(window.clearTimeout(R.current),T.current=new Date().getTime(),R.current=window.setTimeout(I,O))},[I]);g.useEffect(()=>{const O=p.viewport;if(O){const $=()=>{L(N.current),u==null||u()},F=()=>{const W=new Date().getTime()-T.current;N.current=N.current-W,window.clearTimeout(R.current),a==null||a()};return O.addEventListener(nu,F),O.addEventListener(ru,$),()=>{O.removeEventListener(nu,F),O.removeEventListener(ru,$)}}},[p.viewport,C,a,u,L]),g.useEffect(()=>{s&&!p.isClosePausedRef.current&&L(C)},[s,C,p.isClosePausedRef,L]),g.useEffect(()=>(j(),()=>_()),[j,_]);const B=g.useMemo(()=>x?Kv(x):null,[x]);return p.viewport?c.jsxs(c.Fragment,{children:[B&&c.jsx(nk,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:B}),c.jsx(ZN,{scope:n,onClose:I,children:qn.createPortal(c.jsx(Lc.ItemSlot,{scope:n,children:c.jsx(AS,{asChild:!0,onEscapeKeyDown:G(l,()=>{p.isFocusedToastEscapeKeyDownRef.current||I(),p.isFocusedToastEscapeKeyDownRef.current=!1}),children:c.jsx(q.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":p.swipeDirection,...S,ref:d,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:G(e.onKeyDown,O=>{O.key==="Escape"&&(l==null||l(O.nativeEvent),O.nativeEvent.defaultPrevented||(p.isFocusedToastEscapeKeyDownRef.current=!0,I()))}),onPointerDown:G(e.onPointerDown,O=>{O.button===0&&(v.current={x:O.clientX,y:O.clientY})}),onPointerMove:G(e.onPointerMove,O=>{if(!v.current)return;const $=O.clientX-v.current.x,F=O.clientY-v.current.y,W=!!E.current,P=["left","right"].includes(p.swipeDirection),A=["left","up"].includes(p.swipeDirection)?Math.min:Math.max,M=P?A(0,$):0,U=P?0:A(0,F),J=O.pointerType==="touch"?10:2,Le={x:M,y:U},ve={originalEvent:O,delta:Le};W?(E.current=Le,xs(qN,m,ve,{discrete:!1})):Of(Le,p.swipeDirection,J)?(E.current=Le,xs(XN,f,ve,{discrete:!1}),O.target.setPointerCapture(O.pointerId)):(Math.abs($)>J||Math.abs(F)>J)&&(v.current=null)}),onPointerUp:G(e.onPointerUp,O=>{const $=E.current,F=O.target;if(F.hasPointerCapture(O.pointerId)&&F.releasePointerCapture(O.pointerId),E.current=null,v.current=null,$){const W=O.currentTarget,P={originalEvent:O,delta:$};Of($,p.swipeDirection,p.swipeThreshold)?xs(JN,w,P,{discrete:!0}):xs(YN,y,P,{discrete:!0}),W.addEventListener("click",A=>A.preventDefault(),{once:!0})}})})})}),p.viewport)})]}):null}),nk=e=>{const{__scopeToast:t,children:n,...r}=e,o=Zi(Go,t),[s,i]=g.useState(!1),[l,a]=g.useState(!1);return sk(()=>i(!0)),g.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:c.jsx(Qi,{asChild:!0,children:c.jsx(yc,{...r,children:s&&c.jsxs(c.Fragment,{children:[o.label," ",n]})})})},rk="ToastTitle",$v=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return c.jsx(q.div,{...r,ref:t})});$v.displayName=rk;var ok="ToastDescription",Uv=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return c.jsx(q.div,{...r,ref:t})});Uv.displayName=ok;var Bv="ToastAction",Vv=g.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?c.jsx(Hv,{altText:n,asChild:!0,children:c.jsx(Dc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Bv}\`. Expected non-empty \`string\`.`),null)});Vv.displayName=Bv;var Wv="ToastClose",Dc=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=ek(Wv,n);return c.jsx(Hv,{asChild:!0,children:c.jsx(q.button,{type:"button",...r,ref:t,onClick:G(e.onClick,o.onClose)})})});Dc.displayName=Wv;var Hv=g.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return c.jsx(q.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Kv(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),ik(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Kv(r))}}),t}function xs(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Uh(o,s):o.dispatchEvent(s)}var Of=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function sk(e=()=>{}){const t=xt(e);Re(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function ik(e){return e.nodeType===e.ELEMENT_NODE}function lk(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Bl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var ak=Dv,Qv=Iv,Gv=zv,Xv=$v,qv=Uv,Yv=Vv,Jv=Dc;const uk=ak,Zv=g.forwardRef(({className:e,...t},n)=>c.jsx(Qv,{ref:n,className:K("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Zv.displayName=Qv.displayName;const ck=Di("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ey=g.forwardRef(({className:e,variant:t,...n},r)=>c.jsx(Gv,{ref:r,className:K(ck({variant:t}),e),...n}));ey.displayName=Gv.displayName;const dk=g.forwardRef(({className:e,...t},n)=>c.jsx(Yv,{ref:n,className:K("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));dk.displayName=Yv.displayName;const ty=g.forwardRef(({className:e,...t},n)=>c.jsx(Jv,{ref:n,className:K("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:c.jsx(lc,{className:"h-4 w-4"})}));ty.displayName=Jv.displayName;const ny=g.forwardRef(({className:e,...t},n)=>c.jsx(Xv,{ref:n,className:K("text-sm font-semibold",e),...t}));ny.displayName=Xv.displayName;const ry=g.forwardRef(({className:e,...t},n)=>c.jsx(qv,{ref:n,className:K("text-sm opacity-90",e),...t}));ry.displayName=qv.displayName;function fk(){const{toasts:e}=Ac();return c.jsxs(uk,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return c.jsxs(ey,{...s,children:[c.jsxs("div",{className:"grid gap-1",children:[n&&c.jsx(ny,{children:n}),r&&c.jsx(ry,{children:r})]}),o,c.jsx(ty,{})]},t)}),c.jsx(Zv,{})]})}const oy=g.createContext(void 0),el=()=>{const e=g.useContext(oy);if(e===void 0)throw new Error("useAppContext must be used within an AppProvider");return e},pk=({children:e})=>{const[t,n]=g.useState([]),[r,o]=g.useState([]),[s,i]=g.useState(!1),[l,a]=g.useState(null),[u,f]=g.useState("table"),[m,y]=g.useState(!1),[w,S]=g.useState(null),[p,x]=g.useState({status:"",search:"",tags:[],company:"",dateFrom:"",dateTo:""}),h={jobs:t,setJobs:n,tags:r,setTags:o,loading:s,setLoading:i,error:l,setError:a,view:u,setView:f,showForm:m,setShowForm:y,editingJob:w,setEditingJob:S,filters:p,setFilters:x};return c.jsx(oy.Provider,{value:h,children:e})},mk=()=>{const{jobs:e,setJobs:t,tags:n,setTags:r,loading:o,setLoading:s,error:i,setError:l,showForm:a,setShowForm:u,editingJob:f,setEditingJob:m,filters:y}=el(),{toast:w}=Ac();g.useEffect(()=>{S()},[]),g.useEffect(()=>{p()},[y]);const S=async()=>{s(!0);try{await Promise.all([p(),x()])}catch{l("Failed to load data"),w({title:"Error",description:"Failed to load application data",variant:"destructive"})}finally{s(!1)}},p=async()=>{try{const R=await tr.getAll(y);t(R.data)}catch(R){console.error("Failed to load jobs:",R),l("Failed to load jobs")}},x=async()=>{try{const R=await cf.getAll();r(R.data)}catch(R){console.error("Failed to load tags:",R)}},h=async R=>{try{s(!0);const j=await tr.create(R);t(_=>[j.data,..._]),N(),w({title:"Success",description:"Job application created successfully"})}catch(j){console.error("Failed to create job:",j),w({title:"Error",description:"Failed to create job application",variant:"destructive"})}finally{s(!1)}},d=async(R,j)=>{try{s(!0);const _=await tr.update(R,j);t(I=>I.map(L=>L.id===R?_.data:L)),N(),w({title:"Success",description:"Job application updated successfully"})}catch(_){console.error("Failed to update job:",_),w({title:"Error",description:"Failed to update job application",variant:"destructive"})}finally{s(!1)}},v=async R=>{if(confirm("Are you sure you want to delete this job application?"))try{s(!0),await tr.delete(R),t(j=>j.filter(_=>_.id!==R)),w({title:"Success",description:"Job application deleted successfully"})}catch(j){console.error("Failed to delete job:",j),w({title:"Error",description:"Failed to delete job application",variant:"destructive"})}finally{s(!1)}},E=async R=>{try{const j=await cf.create(R);r(_=>[..._,j.data]),w({title:"Success",description:"Tag created successfully"})}catch(j){console.error("Failed to create tag:",j),w({title:"Error",description:"Failed to create tag",variant:"destructive"})}},C=async()=>{try{s(!0);const R=await tr.exportCSV(),j=window.URL.createObjectURL(new Blob([R.data])),_=document.createElement("a");_.href=j,_.setAttribute("download","job_applications.csv"),document.body.appendChild(_),_.click(),_.remove(),window.URL.revokeObjectURL(j),w({title:"Success",description:"CSV exported successfully"})}catch(R){console.error("Failed to export CSV:",R),w({title:"Error",description:"Failed to export CSV",variant:"destructive"})}finally{s(!1)}},T=R=>{m(R),u(!0)},N=()=>{u(!1),m(null)};return c.jsxs("div",{className:"min-h-screen bg-background",children:[c.jsx(fS,{onNewJob:()=>u(!0),onExportCSV:C,loading:o}),i&&c.jsx("div",{className:"bg-destructive/10 border-l-4 border-destructive p-4 mb-4 mx-4",children:c.jsx("div",{className:"flex",children:c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:"text-sm text-destructive",children:i}),c.jsx("button",{onClick:()=>l(null),className:"mt-2 text-sm text-destructive hover:text-destructive/80",children:"Dismiss"})]})})}),c.jsxs("div",{className:"container mx-auto mobile-padding py-8",children:[c.jsx(FN,{tags:n,onCreateTag:E,loading:o}),c.jsx(vS,{jobs:e,onEdit:T,onDelete:v,loading:o})]}),a&&c.jsx(WN,{job:f,tags:n,onSubmit:f?R=>d(f.id,R):h,onClose:N,loading:o}),c.jsx(fk,{})]})},hk=()=>c.jsx(pk,{children:c.jsx(mk,{})});Vl.createRoot(document.getElementById("root")).render(c.jsx(Ft.StrictMode,{children:c.jsx(hk,{})}));
