<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Tracker</title>
    <link rel="stylesheet" href="static/index.css">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
</head>

<body>
    <div id="root"></div>

    <!-- Load shadcn/ui components -->
    <script type="text/babel" src="src/lib/utils.js"></script>
    <script type="text/babel" src="src/components/ui/button.js"></script>
    <script type="text/babel" src="src/components/ui/input.js"></script>
    <script type="text/babel" src="src/components/ui/label.js"></script>
    <script type="text/babel" src="src/components/ui/card.js"></script>
    <script type="text/babel" src="src/components/ui/badge.js"></script>
    <script type="text/babel" src="src/components/ui/dialog.js"></script>
    <script type="text/babel" src="src/components/ui/select.js"></script>
    <script type="text/babel" src="src/components/ui/textarea.js"></script>
    <script type="text/babel" src="src/components/ui/table.js"></script>

    <!-- Load components -->
    <script type="text/babel" src="static/utils/api.js"></script>
    <script type="text/babel" src="static/components/Header.js"></script>
    <script type="text/babel" src="static/components/JobForm.js"></script>
    <script type="text/babel" src="static/components/JobCard.js"></script>
    <script type="text/babel" src="static/components/JobList.js"></script>
    <script type="text/babel" src="static/components/FilterBar.js"></script>
    <script type="text/babel" src="static/app.js"></script>
</body>

</html>