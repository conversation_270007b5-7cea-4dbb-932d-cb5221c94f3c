const Header = ({ onNewJob, onExportCSV, loading }) => {
  const { view, setView, jobs } = useAppContext();
  const [statistics, setStatistics] = useState(null);

  useEffect(() => {
    loadStatistics();
  }, [jobs]);

  const loadStatistics = async () => {
    try {
      const response = await api.get('/statistics');
      setStatistics(response.data);
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  };

  return React.createElement('header', { className: 'bg-background border-b border-border shadow-sm' },
    React.createElement('div', { className: 'container mx-auto px-4 sm:px-6 lg:px-8' },
      React.createElement('div', { className: 'flex flex-col sm:flex-row sm:justify-between sm:items-center py-6 gap-4' },
        // Title and Statistics Section
        React.createElement('div', { className: 'space-y-2' },
          React.createElement('h1', { className: 'text-2xl sm:text-3xl font-bold text-foreground' },
            'JobTrackerPro'
          ),
          statistics && React.createElement('div', { className: 'flex flex-wrap gap-4 text-sm text-muted-foreground' },
            React.createElement('div', { className: 'flex items-center gap-1' },
              React.createElement('span', { className: 'font-medium' }, 'Total:'),
              React.createElement('span', { className: 'text-foreground font-semibold' }, statistics.total_applications)
            ),
            React.createElement('div', { className: 'flex items-center gap-1' },
              React.createElement('span', { className: 'font-medium' }, 'This Month:'),
              React.createElement('span', { className: 'text-foreground font-semibold' }, statistics.applications_this_month)
            ),
            React.createElement('div', { className: 'flex items-center gap-1' },
              React.createElement('span', { className: 'font-medium' }, 'Offers:'),
              React.createElement('span', { className: 'text-green-600 font-semibold' }, statistics.status_counts.Offer || 0)
            ),
            React.createElement('div', { className: 'flex items-center gap-1' },
              React.createElement('span', { className: 'font-medium' }, 'Interviewing:'),
              React.createElement('span', { className: 'text-yellow-600 font-semibold' }, statistics.status_counts.Interviewing || 0)
            )
          )
        ),

        // Actions Section
        React.createElement('div', { className: 'flex flex-col sm:flex-row items-stretch sm:items-center gap-3' },
          // View toggle
          React.createElement('div', { className: 'flex bg-muted rounded-lg p-1' },
            React.createElement(Button, {
              variant: view === 'table' ? 'default' : 'ghost',
              size: 'sm',
              onClick: () => setView('table'),
              className: 'flex-1 sm:flex-none'
            }, 'Table'),
            React.createElement(Button, {
              variant: view === 'cards' ? 'default' : 'ghost',
              size: 'sm',
              onClick: () => setView('cards'),
              className: 'flex-1 sm:flex-none'
            }, 'Cards')
          ),

          // Export button
          React.createElement(Button, {
            variant: 'outline',
            onClick: onExportCSV,
            disabled: loading,
            className: 'w-full sm:w-auto'
          },
            loading ? 'Exporting...' : 'Export CSV'
          ),

          // New job button
          React.createElement(Button, {
            onClick: onNewJob,
            disabled: loading,
            className: 'w-full sm:w-auto'
          }, 'New Application')
        )
      )
    )
  );
};
